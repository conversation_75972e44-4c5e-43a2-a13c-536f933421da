DayOfTheWeek, Month DD, YYYY / The Tcpdump Group
  Summary for 1.11.0 libpcap release (so far!)
    Source code:
      Use C99 fixed-width integer types, rather than self-defined
        fixed-width integer types, in rpcap code.
      Remove an always-false pointer test from snf_read().
      Clean up DECnet address handling.
      struct pcap: Update buffer type from "void *" to "u_char *".
      Remove dead code that supported SITA ACN devices.
      Remove the TODO file.
      Merge four header files into gencode.c.
      Remove DAG card support on Windows as Linux is the only
        platform currently supported.
      Remove code related to Riverbed TurboCap card.
        Riverbed TurboCap hardware has reached EOA as of Jun 30 2014.
      Remove dead code that supported Septel devices.
      Remove outdated code related to AirPcap HW.
        The Riverbed AirPcap Product has reached End of Availability
        (EOA) as of December 31, 2017
      Mind netmap support in pcap_lib_version().
      Make pcap_compile() error messages more uniform and consistent.
    Link-layer types:
      Add LINKTYPE_ETW/DLT_ETW.
      Add LINKTYPE_NETANALYZER_NG/DLT_NETANALYZER_NG (pull request
        #1008).
      Add LINKTYPE_ZBOSS_NCP/DLT_ZBOSS_NCP.
      Add LINKTYPE_USB_2_0_LOW_SPEED/DLT_USB_2_0_LOW_SPEED,
        LINKTYPE_USB_2_0_FULL_SPEED/DLT_USB_2_0_FULL_SPEED,
        LINKTYPE_USB_2_0_HIGH_SPEED/DLT_USB_2_0_HIGH_SPEED
      Add LINKTYPE_AUERSWALD_LOG/DLT_AUERSWALD_LOG.
      Add LINKTYPE_ZWAVE_TAP/DLT_ZWAVE_TAP.
      Add LINKTYPE_SILABS_DEBUG_CHANNEL/DLT_SILABS_DEBUG_CHANNEL.
      Add LINKTYPE_FIRA_UCI/DLT_FIRA_UCI.
      Rename LINKTYPE_IPMB_LINUX/DLT_IPMB_LINUX to
        LINKTYPE_I2C_LINUX/DLT_I2C_LINUX, as it's really just an
        encapsulation of I2C, and is also being used for HDMI DDC.
        Keep DLT_IPMB_LINUX around as a #define for backwards
        compatibility.
    Packet filtering:
      Add support for Block Ack Req and Block Ack frame types (pull
        request #1039).
      Deprecate pcap_compile_nopcap().
      Add support for filtering packets encapsulated with VXLAN (pull
        request #1273).
      Eliminate trailing space in bpf_image() result.
      Fix DECnet packet filtering on big-endian hosts.
      Require "vpi" and "vci" values to be within valid ranges.
      Initialize the scratch memory store to 0.
      Require "[wlan] dir" integer value to be within range.
      Fix the != comparison for ATM and MTP field values.
      Deprecate bpf_filter().
      Require a live capture for all Linux BPF extensions.
      Have "outbound" mean Tx only for DLT_SLIP.
      Filter Linux SocketCAN frames in userland if necessary.
      Enable "[wlan] dir/type/subtype" for DLT_PPI.
      Require "(iso|isis) proto" values to be within valid ranges.
      Use the correct IS-IS PDU type offset for DLT_C_HDLC.
      Use the correct bit mask for IS-IS PDU type value.
      Fix the "|" and "&" operators of the "byte" primitive.
      Require "byte" argument value to be within range.
      Require "link proto" argument value to be within range.
      Require "(ip|ip6) proto" value to be within range.
      Require "protochain" value to be within range.
      Always enable the "gateway" primitive.
      Fix IPv4 multicast filtering to only include *********/4.
    rpcap:
      Support user names and passwords in rpcap:// and rpcaps:// URLs.
      Add a -t flag to rpcapd to specify the data channel port; from
        another incorporate-remote-capture project. (issue #1120)
      rpcapd: Refine SSL options in printusage().
      Fix a possible buffer overflow (Coverity CID 1619148).
    Documentation:
      Document a standard format for writing out BPF filter programs.
      Add a README.hurd.md file.
      Explain "any" device better in pcap_create(3PCAP).
      Cross-reference some man pages better.
      Actualize README.dag and make it Markdown.
      Add a README.snf.md file.
      pcap-filter(7): Make a number of assorted improvements.
      pcap_lib_version(3PCAP): Add details and examples.
      Discuss Linux BPF extensions in the man pages.
      Note endianness in pcap_compile(3PCAP) and pcap_lookupnet(3PCAP).
      man: Document devices, interfaces and "any" better.
    Building and testing:
      Apply GNU Hurd support patch from the Debian package.
      CI: Introduce and use LIBPCAP_CMAKE_TAINTED.
      Remove all remaining support for AOS (4.3BSD Unix), BSD/OS, DOS,
        HP-UX before 10.20, IRIX, {OSF/1, Digital Unix, Tru64 Unix}, SINIX,
        SunOS 3.x, 4.x and 5.x before 5.3.2, and Ultrix.
      Add a new test program for pcap_activate().
      Make NetBSD build warning-free.
      Make Sun C build warning-free.
      Make FreeBSD, Linux and macOS builds warning-free.
      Print MAC addresses in findalldevstest.
      Parameterize the interface name in reactivatetest.
      When necessary, trust the OS to implement ffs().
      At build time require a proof of suitable snprintf(3) implementation in
        libc (and document Solaris 9 and MinGW as unsupported because of that).
      Makefile.in: Update the .c.o build rule (Remove hacks for old SunOS 4).
      CMake: Skip snprintf(3) tests when cross-compiling; test whether
        check_c_source_runs() with a trivial program and, if it doesn't
        work, don't run tests that use it.
      autogen.sh: Allow to configure Autoconf warnings.
      autogen.sh: Delete all trailing blank lines at end of configure.
      Autoconf: Add support for libnl-tiny with pkg-config.
      Autoconf: Fix building of something-only libpcap.
      CI: Implement "make check".
      Fix autotools and CMake issues with snprintf test and sanitizers.
        Fixes issue #1396.
      filtertest: Add "-l" flag to use Linux BPF extensions.
      filtertest: Use ntohl() on the netmask to fix the -m option.
      filtertest: Add support for stdin as "-" to the -F option.
      Rename CMake option to ENABLE_PROTOCHAIN to match Autoconf.
      Remove the "disable IPv6" build option.
    Hurd:
      Support network capture devices too.
      Fix a few device activation bugs.
      Count and timestamp packets better.
      Add kernel filtering, fix userland filtering.
    OpenBSD:
      Use getprotobyname_r() correctly on OpenBSD.
    Linux:
      Do not use ether_hostton() from musl libc.
      Fix propagation of getprotobyname_r() errors.
    Haiku:
      Look for ethers(5) in /boot/system/settings/network/.
    DAG:
      Always set PCAP_IF_CONNECTION_STATUS_NOT_APPLICABLE.
      In dag_findalldevs() handle known errors better.
      Trust dag_get_stream_erf_types() to be available.
      Require the API to have 64-bit streams support.
      Make device descriptions more useful, make dagN conditional.
      Use PCAP_ERROR_NO_SUCH_DEVICE more in dag_activate().
      Validate capture device names better.
      Enumerate streams without trying to lock them.
      If the environment variable ERF_FCS_BITS is set, require it to be either
        0 or 16 or 32.  Likewise for ERF_DONT_STRIP_FCS (either 0 or 1).
      Remove FCS quirks specific to 4.2S and 4.23S.
      Fix packet filtering with low snaplen.
      Fix ps_drop for stream drop counters.
      Add experimental packet transmit support.
    SNF:
      Fix packet filtering with low snaplen.
      Require SNF_VERSION_API >= 0x0003.
      Improve device descriptions and flags.
      Fill pcap_if_t in more consistently.

DayOfTheWeek, Month DD, YYYY / The Tcpdump Group
  Summary for 1.10.6 libpcap release (so far!)
    Source code:
      Fix "tcpdump -i <n>" for something-only libpcap builds.
      Remove some unneeded includes.
      pcapint_find_function() changed to return "void *" to avoid
        warnings.
      gencode: Fix an undefined behavior in gen_mcode().
      gencode: Add a missing free() in gen_scode().
      Remove "DLT_" from the descriptions of two dlt_choices[] entries.
    Packet filtering:
      Make the chunk allocator's alignment more general and
        platform-independent.
      IEEE 802.11: Fix three undefined behaviors in grammar.y.
    Linux:
      Fix check for mac80211 phydev.
      Don't create monitor-mode interface if we're capturing on one.
      Expand the table of DSA tag types to include all current types.
      Fix an error message when deleting a monN interface.
      Fix returning PCAP_ERROR_RFMON_NOTSUP with libnl.
      Fix the error message when capure permission is denied.
      Fix the error message if PF_PACKET sockets aren't supported.
      Fix a file descriptor leak in an error case (pull request #1537).
    FreeBSD:
      Fix detection and enablng of zero-copy supporrt.
      Fix errors in the zero-copy code.
    Solaris:
      Fix not to ignore logical interfaces in fad-gifc.c and
        fad-glifc.c.
      Fix attempts to open all-numeric device names with DLPI to
        return "no such device".
      Fix error returns and messages when an interface has no DLPI
        device.
      Return all interfaces in pcap_findalldevs() even if they can't be
        opened.
    HP-UX:
      Fix attempts to open all-numeric device names to return
        "no such device".
      Fix error message if there's no /dev/dlpi device.
      Return all interfaces in pcap_findalldevs() even if they can't be
        opened.
    Windows:
      Fix filtering for VLAN-tagged frames.
      Add support for Npcap's nanosecond-resolution time stamps in
        captures.
    D-Bus:
      Fix message leak.
    Capture file writing:
      Don't close the output stream if it's stdout, just flush it.
    Documentation:
      Explicitly document that closing a pcap_t for a savefile opened
        with pcap_fopen_offline() will close the standard I/O stream.
    Building and testing:
      Makefile.in: Include instrument-functions.c in the release tarball.
      CMake: Fix libnl usage with pkg-config.
      CMake: Fix build with CMake 3.31.
      CI: Report CMake version in builds.
      CI: Visual Studio 2022 builds added, including ARM64 builds;
        Visual Studio 2015 builds dropped.
      Don't build with sslutils.c if we don't have a TLS library.
      Build on Windows with a newer version of OpenSSL.
      CMake: generalize handling of non-x86 Windows architectures.
      CI: use the -A flag for all Visual Studio generators.
      Remove the fuzzing props from the release tarball.
      Autoconf: Use AC_SYS_YEAR2038_RECOMMENDED when possible if the
        environment variable BUILD_YEAR2038 = yes (via autogen.sh)

Friday, August 30, 2024 / The Tcpdump Group
  Summary for 1.10.5 libpcap release
    Source code:
      Spell WirelessHART details properly.
      Mark pcap_vasprintf() as printf-like.
      Finalize moving of bpf_filter.c. (GH #1166)
      Remove an unneeded argument from gen_mcode6().
      Don't do some Berkeley YACC workarounds with YACC releases not
        requiring them.
      Use correct data types rather than int in some cases.
      Squelch compiler warning in grammar.c.
      Fix findalldevtest compilation if IPv6 isn't enabled.
      Rename helper routines for pcap modules to have names beginning with
        pcapint_, to avoid namespace collisions for code linking statically
        with libpcap.
      Avoid casting hack for the Windows cleanup-on-exit routine.
      Use %zu format for one case of printing a size_t.
      Fix some Coverity errors.
      Fix availabilities of some functions to match reality.
      pcap: make the seconds and microseconds/nanoseconds fields unsigned.
      Remove the unused pcap-rpcap-int.h header file.
    Thread safety:
      Make some static variables thread-local; fixes issue #1174.
    Packet filtering:
      Improve reporting of some invalid filter expressions.
      Return an error from pcap_compile() if the scanner fails to initialize.
      Optimizer fix from Archit Shah to recompute dominators after
        moving code (#976); fixes #945 (although the resulting filter
        isn't empty).
      Optimizer fix from Archit Shah to mark value as unknown when store
        of that value is deleted (#972); fixes #143, #434, #436, #437,
        and #1076.
    Linux:
      Properly return warnings.
      Don't use DLT_LINUX_SLL2 for anything other than the "any" device.
      Avoid 32-bit unsigned integer overflow in USB captures.  Fixes
        issues #1134 and #1205.
      Fix a file descriptor leak.
      Properly report warnings about unknown ARPHRD_ types.
      Fix DLT_CAN_SOCKETCAN handling of CAN FD.
      Add CAN XL support to DLT_CAN_SOCKETCAN.
      Clean up the code that sets the "real" ("original") length for
        isochronous USB transfers.
      Avoid unnecessary blocking on recvmsg() in the Bluetooth monitor and
        Bluetoth modules.
    Solaris:
      Handle BPF returning ESRCH for unknown devices.
      List the "any" device if it's supported.
      Report {non-existent zone}/{interface} errors appropriately.
      Allow attaching to links owned by a non-global zone.  (Based on
        pull request #1202.)
      Fix AF_LINK handling on illumos.
    macOS:
      Redid the availability macros to be closer to what Apple's doing
        in recent SDKs, including tagging pcap-namedb.h routines.
      Fix the install name of the installed shared library to have a
        full path when building with CMake.
      Fix universal builds.
    Haiku:
      Convert the module to C.  Fixes issue #1114.
      Address a few compiler warnings.  Fixes issue #1114.
      Fix various build problems.  Fixes issue #1114.
      Report non-existent devices correctly.
      Fix handling of packet statistics.
      Fix packet timestamping.
      Fix packet filtering with low snaplen.
      Improve connection status reporting.
      Add support for promiscuous mode.
      Detect DLTs and loopback capture support at run time.
      Report IEEE 802.11 as PCAP_IF_WIRELESS.
    Windows:
      Fix internal handling of "not supported" error codes from NPF.
      Work around a bug in Npcap 1.00 in case of driver version mismatch.
      Don't call WSACleanup() when handling a failed WSAStartup().
    BSD, macOS, AIX, Solaris 11, Linux:
      Add a new error PCAP_ERROR_CAPTURE_NOTSUP, for use if a capture
        mechanism is not present, in the hopes that, for example,
        attempts to capture on Windows Services for Linux 1, in which
        the NT kernel attempts to simulate Linux system calls but does
        not support packet sockets, can get an error that better
        indicates the underlying problem.
    AirPcap:
      Format an error message if we run out of memory.
    nflog:
      Fix count of dropped packets.
      Make sure we don't overflow when rounding up the TLV length.
    rpcap:
      Handle routines removed in at least some OpenSSL libraries.
      CVE-2023-7256: Clean up sock_initaddress() and its callers to avoid
        double frees in some cases.
      Don't define SOCKET ourselves; instead, define PCAP_SOCKET as int
        on UN*Xes and as SOCKET on Windows.
      CVE-2024-8006: Fix pcap_findalldevs_ex() not to crash if passed a
        file:// URL with a path to a directory that cannot be opened.
    Savefiles:
      Handle DLT_/LINKTYPE_ mapping better, to handle some
        OpenBSD-specific link types better.
      Treat if_tsoffset as signed in pcapng files, as the spec says.
      Don't try to fix the "real" length for isochronous USB
        transfers if the number of USB descriptors is too large.
      Reject pcap files where one of the reserved fields in the
        "link-layer type plus other stuff" is non-zero.
    Building and testing:
      Add a configure option to help debugging (--enable-instrument-functions).
      Improved tests and error reporting for uses of pkg-config, and
        improve help message.
      Fix Haiku build.
      With CMake, install headers in CMAKE_INSTALL_INCLUDEDIR rather
        than just include.
      Build libpcap.a before building test programs.
      Print address family numerically, as well as symbolically,
        in findalldevstest.
      Fail with suggestions, rather than failing over to no capture
        support, if no capture mechanism was found.  Fixes issue #1016.
      Don't indent comments in Make, as that may cause them not to be
        recognized as comments.
      Don't check for libssl if we aren't going to use it.
      Better handle enabling and disabling of sanitizers.  Fixes issue
        #1171.
      CMakeLists.txt: Print "Symlinking: /some/path to ..." conditionally.
      Evaluate CMAKE_INSTALL_PREFIX at install time.
      cmake: Update the minimum required version to 2.8.12 (except Windows).
      cmake: suppress CMP0042 OLD deprecated warning.
      Makefile.in: Add the releasecheck target.
      Cirrus CI: Add the "make releasecheck" command in the Linux task.
      Makefile.in: Add the whitespacecheck target.
      Cirrus CI: Run the "make whitespacecheck" command in the Linux task.
      Autoconf: Update config.{guess,sub}, timestamps 2024-01-01.
      Autoconf: Update the install-sh script to the 2020-11-14.01 version.
      Compile with '-Wnull-pointer-subtraction',
        '-Wunused-but-set-parameter', and '-Wunused-but-set-variable' in
        devel mode if supported.
      Don't ignore spaces between CMAKE_C_FLAGS and DPDK_C_FLAGS with
        CMake.
      Use noreturn and __format__ with XL C 7.0 and later.
      Check for the same -W flags in autotools and CMake.
      Autoconf: Add autogen.sh, remove configure and config.h.in and put
        these generated files in the release tarball.
      Autoconf: Get the size of a time_t.
      Fix propagation of cc_werr_cflags() output.
      Makefile.in(s): Fix the depend target.
      mkdep: Exit with a non-zero status if a command fails.
      Fix HCI_CHANNEL_MONITOR detection with musl libc.
      Extend "make shellcheck" onto mkdep too.
      Add initial support for building with TinyCC.
      Address all known compiler warnings specific to illumos, Linux, NetBSD,
        Solaris and Sun C; in CI expect warnings specific to TinyCC only.
    Documentation:
      Update and fix pcap-filter man page.
      Add a README.haiku.md file.
      Document pcap-config better.
      Man page formatting and prose fixes.
      Rename doc/README.Win32.md to doc/README.windows.md.
      Update pcap-savefile man page to match the Internet-Draft for
        pcap.
      Fix CMake issues for target used by other projects.
      Explain "any" device better in pcap_open_live(3PCAP).
      Update INSTALL.md.
      Note in man pages that errbuf arguments must point to an error
        buffer.
      Note that if pcap_findalldevs() fails it sets *alldevsp to NULL;
        there's no devices list to free.
      Explain "other addresses" in pcap_findalldevs(3PCAP).
      Document pcap_lookupnet(3PCAP) a bit better.

Friday, April 7, 2023 / The Tcpdump Group
  Summary for 1.10.4 libpcap release
    Source code:
      Fix spaces before tabs in indentation.
    rpcap:
      Fix name of launchd service.
    Documentation:
      Document use of rpcapd with systemd, launchd, inetd, and xinetd.
    Building and testing:
      Require at least pkg-config 0.17.0, as we use --static.
      Get rid of the remains of gnuc.h.
      Require at least autoconf 2.69.
      Update config.{guess,sub}, timestamps 2023-01-01,2023-01-21.

Thursday, January 12, 2023 / The Tcpdump Group
  Summary for 1.10.3 libpcap release
    Source code:
      Sort the PUBHDR variable in Makefile.in in "ls" order.
      Fix typo in comment in pflog.h.
      Remove two no-longer-present files from .gitignore.
      Update code and comments for handling failure to set promiscuous
        mode based on new information.
    Building and testing:
      install: Fixed not to install the non-public pcap-util.h header.
      pcap-config: add a --version flag.
      Makefile.in: Add some missing files in the distclean target.

Saturday, December 31, 2022 / The Tcpdump Group
  Summary for 1.10.2 libpcap release
    Source code:
      Use __builtin_unreachable() in PCAP_UNREACHABLE.
      Use AS_HELP_STRING macro instead of AC_HELP_STRING in the
        configure scripts, to avoid deprecation warnings.
      Change availability tags in pcap.h to make it easier to
        arrange for it to be used in Darwin releases.
      Use AS_HELP_STRING for --enable-remote.
      Fix some formatting string issues found by cppcheck.
      Various small code and comment cleanups.
      Use PCAP_ERROR (defined as -1) rather than explicit -1 for
        functions the documentation says return PCAP_ERROR.
      Remove unused code from the filter compiler.
      Use _declspec(deprecated(msg)) rather than __pragma(deprecated)
        for Windows deprecation warnings, so the message that was
        specified shows up.
      diag-control.h: define PCAP_DO_PRAGMA() iff we're going to use it.
      Use "%d" to print some signed ints.
      Use the Wayback Machine for a removed document in a comment.
      Add some const qualifiers.
      RDMA: Use PRIu64 to print a uint64_t.
    "Dead" pcap_ts from pcap_open_dead() and ..._with_tstamp_precision():
        Don't crash if pcap_breakloop() is called.
    Savefiles:
      Fix pcap_dispatch() to return number of packets processed, rather
        than 0, even at EOF.
      If we get an error writing the packet header, don't write the
        packet data.
      Put PFLOG UID and PID values in the header into host byte order
        when reading a LINKTYPE_PFLOG file.
      Put CAN ID field in CAN pseudo-headers for LINUX_SLL2, as we do
        for LINUX_SLL.
      Fix incorrectly-computed "real" length for isochronous USB
        transfers when reading savefiles.
      Don't crash if pcap_can_set_rfmon() is called.
      Fix pcap_offline_read() loop.
    Capture:
      Never process more than INT_MAX packets in a pcap_dispatch() call,
        to avoid integer overflow (issue #1087).
      Improve error messages for "no such device" and "permission
        denied" errors.
      SITA: Fix a typo in a variable name.
    Packet filtering:
      Get PFLOG header length from the length value in the header.
      Support all the direction, reason, and action types supported by
        all systems that support PFLOG.
      Don't require PFLOG support on the target machine in order to
        support PFLOG filtering (also fixes issue #1076).
      Expand abbreviations into "proto X" properly.
      gencode.c: Update a comment about the VLAN TPID test.
      Add the minimum and maximum matching DLTs to an error message.
    Linux:
      Fix memory leak in capture device open (pull request #1038).
      Fix detection of CAN/CAN FD packets in direction check (issue
        #1051).
      Fix double-free crashes on errors such as running on a kernel with
        CONFIG_PACKET_MMAP not configured (issue #1054).
      Use DLT_CAN_SOCKETCAN for CANbus interfaces (issue #1052; includes
        changes from pull request #1035).
      Make sure the CANFD_FDF can be relied on to indicate whether a
        CANbus packet is a CAN frame or a CAN FD frame
      Improve error message for "out of memory" errors for kernel
        filters (see issue #1089).
      Fix pcap_findalldevs() to find usbmon devices.
      Fix handling of VLAN tagged packets if the link-layer type is
        changed from DLT_LINUX_SLL to DLT_LINUX_SLL2 (see issue #1105).
      Always turn on PACKET_AUXDATA (see issue #1105).
      We require 2.6.27 or later, so PACKET_RESERVE is available.
      Make sure there's reserved space for a DLT_LINUX_SLL2 header
        when capturing.
      Correctly compute the "real" length for isochronous USB transfers.
      Don't have an eventfd descriptor open in non-blocking mode, so as
        not to waste descriptors.
      netfilter: Squelch a narrowing warning (To be look at before 2038).
    BPF capture (*BSD, macOS, AIX, Solaris 11):
      Fix case where a device open might fail, rather than falling back
        to a smaller buffer size, when the initial buffer size is too
        big.
      Use an unsigned device number to iterate over BPF devices, to
        squelch a compiler warning.
    NetBSD:
      Fix handling of LINKTYPE_HDLC/DLT_HDLC.
    rpcap:
      Fix unaligned accesses in rpcapd (pull request #1037).
      Fix code to process port number.
      Clean up findalldevs code in rpcapd.
      Clean up bufferizing code.
      Fix a file descriptor/handle leak in pcap_findalldevs_ex()
        (Coverity CID 1507240).
      Improve error messages for host and port resolution errors.
      Fix connect code not to fail if both IPv4 and IPv6 addresses are
        tried.
      Improve connect failure error message.
      Provide an error message for a bad authentication reply size.
      For link-layer types with host-endian fields in the header, fix
        those fields if capturing from a server with a different byte
        order.
      Suppress temporarily the warnings with "enable remote packet capture".
    Windows:
      Add support for NdisMediumIP (pull request #1027).
      Don't require applications using pcap to be built with VS 2015 or
        later.
      Use the correct string for the DLL VersionInfo.
      Remove unnecessary DllMain() function.
      Correctly handle ERROR_INVALID_FUNCTION from
        PacketGetTimestampModes() (indicate that WinPcap or an older
        version of Npcap is probably installed).
      Fix use-after-free in some cases when a pcap_t is closed.
      Make sure an error is returned by pcap_create_interface() if
        PacketOpenAdapter() fails.
      Return an error if the driver reports 0 timestamp modes supported.
      Close the ADAPTER handle for some errors in
        pcap_create_interface().
      Get rid of old unmaintained VS project files.
      Fix deprecation warning for pcap_handle().
      Npcap is now at npcap.com, not npcap.org.
      Make sure "no such device" and "no permission to open device"
        errors show up in pcap_activate(), not pcap_create() (fixes,
        among other things, tcpdump -i <interface-number>).
      npcap: squelch deprecation warnings for kernel dump mode.
    Haiku:
      Implement pcap_lib_version(), as now required.
      Handle negative or too-large snaplen values.
      Fix various build issues and warnings.
    Building and testing:
      Update configure-time universal build checks for macOS.
      Update config.guess and config.sub.
      If we look for an SSL library with pkg-config in configure script,
        try pkg-config first.
      If we have pkg-config and Homebrew, try to set pkg-config up to
        find Homebrew packages.
      Handle some Autoconf/make errors better.
      Use "git archive" for the "make releasetar" process.
      Remove the release candidate rcX targets.
      Fix compiling on Solaris 9/SPARC and 11/AMD64.
      Address assorted compiler warnings.
      Fix cross-building on Linux for Windows with mingw32 for Win64
        (pull request #1031).
      Properly set installation directory on Windows when not compiling
        with MSVC.
      Fix configure script checks for compiler flags.
      Give more details if check for usable (F)Lex fails.
      Fix compiling with GCC 4.6.4.
      Don't use add_compile_options() with CMake, as we currently don't
        require 2.8.12, where it first appeared.
      Don't provide -L/usr/lib for pkg-config --libs in pkg-config.
      Fix error message for inadequate Bison/Berkeley YACC.
      configure: correctly do some DPDK checks.
      Only use pkg-config when checking for DPDK.
      Allow the path in which DPDK is installed to be specified.
      Use pkg-config first when checking for libibverbs.
      CMake: fix check for libibverbs with Sun's C compiler.
      Have CMake warn if no capture mechanism can be found.
      Don't do stuff requiring 3.19 or later on earlier CMakes.
      Squelch some CMake warnings.
      Fix diag-control.h to handle compiling with clang-cl (issues
        #1101 and #1115).
      Cleanup various leftover cruft in the configure script.
      Fix building without protochain support. (GH #852)
      Check for a usable YACC (or Bison) and {F}lex in CMake, as we do
        in autotools.
      Only check for a C++ compiler on Haiku, as that's the only
        platform with C++ code, and make sure they generate code for
        the same instruction set bit-width (both 32-bit or both 64-bit)
        (issue #1112).
      On Solaris, check the target bit-width and set PKG_CONFIG_PATH
        appropriately, to handle the mess that is the D-Bus library
        package (issue #1112).
      Fix generation of pcap-config and libpcap.pc files (issue #1062).
      pcap-config: don't assume the system library directory is /usr/lib.
      pcap-config: add a --static-pcap-only flag.
      Cirrus CI: Use the same configuration as for the main branch.
      Add four libpcap test files.
      Update Npcap SDK to 1.13.
      Makefile.in: Use TEST_DIST, like for tcpdump.
      Remove awk code from mkdep.
      Cirrus CI: Add the libssl-dev package in the Linux task.
      Cirrus CI: Add the openssl@3 brew package in the macOS task.
      Get "make shellcheck" to pass again.
      CMake: Build valgrindtest only if Autoconf would.
      CMake: use ${CMAKE_INSTALL_SBINDIR} rather than just sbin.
      CMake: use NUL: as the null device on Windows.
      autoconf: fix typo in test of macOS version.
      Makefile.in: Add two missing files in EXTRA_DIST.
      autotools, cmake: provide an rpath option if necessary.
      configure: get rid of the attempt to auto-run PKG_PROG_PKG_CONFIG.
      configure: use PKG_CHECK_MODULES to run pkg-config.
    Documentation:
      Add README.solaris.md.
      Add SCTP to pcap-filter(7).
      Note that = and == are the same operator in filters (issue #1044).
      Update INSTALL.md, README.md, and README.solaris.md.
      Update and clean up CONTRIBUTING.md.
      Trim documentation of support for now-dead UN*Xe and older
        versions of other UN*Xes.
      Move the "how to allocate a LINKTYPE_/DLT_ value" documentation to
        the web site.
      Clean up man pages.
      Move README.capture-module to the web site.
      Improve some protocol details in pcap-filter(7).
      Refine "relop" notes in pcap-filter(7).
      In pcap-filter(7) "domain" is an id.
      Discuss backward compatibility in pcap-filter(7).
      Other improvements to pcap-filter(7).
      Document pcap_breakloop(3PCAP) interaction with threads better.
      Document PCAP_ERROR_NOT_ACTIVATED for more routines.

Wednesday, June 9, 2021:
  Summary for 1.10.1 libpcap release:
    Packet filtering:
      Fix "type XXX subtype YYY" giving a parse error
    Source code:
      Add PCAP_AVAILABLE_1_11.
    Building and testing:
      Rename struct bpf_aux_data to avoid NetBSD compile errors
      Squelch some compiler warnings
      Squelch some Bison warnings
      Fix cross-builds with older kernels lacking BPF_MOD and BPF_XOR
      Fix Bison detection for minor version 0.
      Fix parallel build with FreeBSD make.
      Get DLT_MATCHING_MAX right in gencode.c on NetBSD.
      Define timeradd() and timersub() if necessary.
      Fix Cygwin/MSYS target directories.
      Fix symlinking with DESTDIR.
      Fix generation of libpcap.pc with CMake when not building a shared
          library.
      Check for Arm64 as well as x86-64 when looking for packet.lib on
          Windows.
    Documentation:
      Refine Markdown in README.md.
      Improve the description of portrange in filters.
      README.linux.md isn't Markdown, rename it just README.linux.
    pcapng:
      Support reading version 1.2, which some writers produce, and which
          is the same as 1.0 (some new block types were added, but
          that's not sufficient reason to bump the minor version number,
          as code that understands those new block types can handle them
          in a 1.0 file)
    Linux:
      Drop support for text-mode USB captures, as we require a 2.6.27
          or later kernel (credit to Chaoyuan Peng for noting the
          sscanf vulnerabilities in the text-mode code that got me to
          realize that we didn't need this code any more)
      Bluetooth: fix non-blocking mode.
      Don't assume that all compilers used to build for Linux support
          the __atomic builtins
    Windows:
      Add more information in "interface disappeared" error messages, in
        the hopes of trying to figure out the cause.
      Treat ERROR_DEVICE_REMOVED as "device was removed".
      Indicate in the error message which "device was removed" error
          occurred.
      Report the Windows error status if PacketSendPacket() fails.
      Use %lu for ULONGs in error message formats.
      Don't treat the inability to find airpcap.dll as an error.
      Ignore spurious error reports by Microsoft Surface mobile
          telephony modem driver
    rpcap:
      Clean up error checking and error messages for server address
          lookup.

Tuesday, December 29, 2020
  Summary for 1.10.0 libpcap release
    Add support for capturing on DPDK devices
    Label most APIs by the first release in which they're available
    Fix some memory leaks, including in pcap_compile()
    Add pcap_datalink_val_to_description_or_dlt()
    Handle the pcap private data in a fashion that makes fewer
       assumptions about memory layouts (might fix GitHub issue #940
       on ARM)
    Fix some thread safety issues
    pcap_findalldevs(): don't sort interfaces by unit number
    Always return a list of supported time-stamp types, even if only
        host time stamps are supported
    Increase the maximum snaplen for LINKTYPE_USBPCAP/DLT_USBPCAP
    Report the DLT description in error messages
    Add pcap_init() for first-time initialization and global option
        setting; it's not required, but may be used
    Remove (unused) SITA support
    Capture file reading:
        Correctly handle pcapng captures with more than one IDB with a
            snapshot length greater than the supported maximum
    Capture file writing:
        Create the file in pcap_dump_open_append() if it doesn't exist
    Packet filtering:
        Fix "unknown ether proto 'aarp'"
        Add a new filter "ifindex" for DLT_LINUX_SLL2 files on all
            platforms and live Linux captures
        Add a hack to the optimizer to try to catch certain optimizer
            loops (should prevent GitHub issue #112)
        Show special Linux BPF offsets symbolically in bpf_image() and
            bpf_dump()
        Added support for ICMPv6 types 1-4 as tokens with names
        Remove undocumented and rather old "ether proto" protocols
        Catch invalid IPv4 addresses in filters
        Don't assume ARM supports unaligned accesses
    Security and other issues found by analysis:
        Fix various security issues reported by Charles Smith at Tangible
            Security
        Fix various security issues reported by Include Security
        Fix some issues found by cppcheck.
        Add some overflow checks in the optimizer
    rpcap:
        Support rpcap-over-TLS
        Redo protocol version negotiation to avoid problems with old
            servers (it still works with servers using the old negotiation,
            as well as servers not supporting negotiation)
        Error handling cleanups
        Add some new authentication libpcap error codes for specific
            errors
        Fix some inetd issues in rpcapd
        Fix rpcapd core dumps with invalid configuration file
        On UN*X, don't have rpcapd tell the client why authentication
            failed, so a brute-force attacker can't distinguish between
            "unknown user name" and "known user name, wrong password"
        Allow rpcapd to rebind more rapidly (GitHub issue #765)
    Documentation:
        Improve man pages, including adding backward compatibility notes
    Building and testing:
        Require, and assume, some level of C99 support in the C compiler
        Require Visual Studio 2015 or later if using Visual Studio
        Fix configure script issues, including with libnl on Linux
        Fix CMake issues
        Squelch complaints from Bison about "%define api.pure" being
            deprecated
        Fix compilation of pcap-tc.c
    Linux:
        Require PF_PACKET support, and kernel 2.6.27 or later
        Handle systems without AF_INET or AF_UNIX socket support
        Get rid of Wireless Extensions for turning monitor mode on
        Proper memory sync for PACKET_MMAP (may prevent GitHub issue
            #898)
        Drop support for libnl 1 and 2.
        Return error on interface going away, but not if it just went
            down but is still present
        Set socket protocol only after packet ring configured,
            reducing bogus packet drop reports
        Get ifdrop stats from sysfs.
        When adjusting BPF programs, do not subtract the
            SLL[2]_HDR_LEN if the location is negative (special metadata
            offset), to preserve references to metadata; see
            https://github.com/the-tcpdump-group/tcpdump/issues/480#issuecomment-486827278
        Report a warning for unknown ARPHRD types
        Have pcap_breakloop() forcibly break out of a sleeping
            capture loop
        Add support for DSA data link types
        For raw USB bus capture, use the snapshot length to set the
            buffer size, and set the len field to reflect the length
            in the URB (GitHub issue #808)
        With a timeout of zero, wait indefinitely
        Clean up support for some non-GNU libc C libraries
        Add DLT_LINUX_SLL2 for cooked-mode captures
        Probe CONFIGURATION descriptor of connected USB devices
        Treat EPERM on ethtool ioctls as meaning "not supported", as
            permissions checks are done before checking whether the
            ioctl is supported at all
    macOS:
        Cope with getting EPWROFF from SIOCGIFMEDIA
        Treat EPERM on SIOCGIFMEDIA as meaning "not supported", as
            permissions checks are done before checking whether the
            ioctl is supported at all
        Treat ENXIO when reading packets as meaning "the interface
            was removed"
        Report "the interface disappeared", not "the interface went
            down", if the interface was removed during a capture
    FreeBSD:
        Treat ENXIO as meaning "the interface was removed"
        Report "the interface disappeared", not "the interface went
            down", if the interface was removed during a capture
    NetBSD:
        Treat ENXIO as meaning "the interface was removed"
        Report "the interface disappeared", not "the interface went
            down", if the interface was removed during a capture
    OpenBSD:
        Treat EIO as meaning "the interface was removed"
        Report "the interface disappeared", not "the interface went
            down", if the interface was removed during a capture
    DragonFly BSD:
        Treat ENXIO as meaning "the interface was removed"
        Report "the interface disappeared", not "the interface went
            down", if the interface was removed during a capture
    Solaris:
        Treat ENXIO as meaning "the interface was removed"
        Report "the interface disappeared", not "the interface went
            down", if the interface was removed during a capture
    AIX:
        Fix loading of BPF kernel extension
        Treat ENXIO as meaning "the interface was removed"
        Report "the interface disappeared", not "the interface went
            down", if the interface was removed during a capture
    Windows:
        Make the snapshot length work even if pcap_setfilter()
            isn't called
        Fix compilation on Cygwin/MSYS
        Add pcap_handle(), and deprecate pcap_fileno()
        Report PCAP_ERROR_NO_SUCH_DEVICE for a nonexistent device
        Return an appropriate error message for device removed or
            device unusable due to a suspend/resume
        Report a warning for unknown NdisMedium types
        Have pcap_breakloop() forcibly break out of a sleeping
            capture loop
        Clean up building DLL
        Handle CRT mismatch for pcap_dump_fopen()
        Map NdisMediumWirelessWan to DLT_RAW
        Add AirPcap support in a module, rather than using
            WinPcap/Npcap's support for it
        Report the system error for PacketSetHwFilter() failures
        Add support for getting and setting packet time stamp types
            with Npcap
        Have pcap_init() allow selecting whether the API should use
            local code page strings or UTF-8 strings (including error
            messages)
    Haiku:
        Add capture support

Sunday, July 22, 2018
  Summary for 1.9.1 libpcap release
    Mention pcap_get_required_select_timeout() in the main pcap man page
    Fix pcap-usb-linux.c build on systems with musl
    Fix assorted man page and other documentation issues
    Plug assorted memory leaks
    Documentation changes to use https:
    Changes to how time stamp calculations are done
    Lots of tweaks to make newer compilers happier and warning-free and
        to fix instances of C undefined behavior
    Warn if AC_PROG_CC_C99 can't enable C99 support
    Rename pcap_set_protocol() to  pcap_set_protocol_linux().
    Align pcap_t private data on an 8-byte boundary.
    Fix various error messages
    Use 64-bit clean API in dag_findalldevs()
    Fix cleaning up after some errors
    Work around some ethtool ioctl bugs in newer Linux kernels (GitHub
        issue #689)
    Add backwards compatibility sections to some man pages (GitHub issue
        #745)
    Fix autotool configuration on AIX and macOS
    Don't export bpf_filter_with_aux_data() or struct bpf_aux_data;
        they're internal-only and subject to change
    Fix pcapng block size checking
    On macOS, don't build rpcapd or test programs any fatter than they
        need to be
    Fix reading of capture statistics for Linux USB
    Fix packet size values for Linux USB packets (GitHub issue #808)
    Check only VID in VLAN test in filters (GitHub issue #461)
    Fix pcap_list_datalinks on 802.11 devices on macOS
    Fix overflows with very large snapshot length in pcap file
    Improve parsing of rpcapd configuration file (GitHub issue #767)
    Handle systems without strlcpy() or strlcat() better
    Fix crashes and other errors with invalid filter expressions
    Fix use of uninitialized file descriptor in remote capture
    Fix some CMake issues
    Fix some divide-by-zero issues with the filter compiler
    Work around a GNU libc bug in pcap_nametonetaddr()
    Add support for DLT_LINUX_SLL2
    Fix handling of the packet-count argument for Myricom SNF devices
    Fix --disable-rdma in configure script (GitHub issue #782)
    Fix compilation of TurboCap support (GitHub issue #764)
    Constify first argument to pcap_findalldevs_ex()
    Fix a number of issues when running rpcapd as an inetd-style daemon
    Fix CMake issues with D-Bus libraries
    In rpcapd, clean up termination of a capture session
    Redo remote capture protocol negotiation
    In rpcapd, report the same error for "invalid user name" and
        "invalid password", to make brute-forcing harder
    For remote captures, add an error code for "the server requires TLS"
    Fix pcap_dump_fopen() on Windows to avoid clashes between
        {Win,N}Pcap and application C runtimes
    Fix exporting of functions from Windows DLLs (GitHub issue #810)
    Fix building as part of Npcap
    Allow rpcapd to rebind more rapidly
    Fix building shared libpcap library on midipix (midipix.org)
    Fix hack to detect UTF-16LE adapter names on Windows not to go past
        the end of the string
    Fix handling of "wireless WAN" (mobile phone network modems) on
        Windows with WinPcap/Npcap (GitHub issue #824)
    Have pcap_dump_open_append() create the dump file if it doesn't
        exists (GitHub issue #247)
    Fix the maximum snapshot length for DLT_USBPCAP
    Use -fPIC when building for 64-bit SPARC on Linux (GitHub issue #837)
    Fix CMake 64-bit library installation directory on some Linux
        distributions
    Boost the TPACKET_V3 timeout to the maximum if a timeout of 0 was
        specified
    Five CVE-2019-15161, CVE-2019-15162, CVE-2019-15163, CVE-2019-15164, CVE-2019-15165
    PCAPNG reader applies some sanity checks before doing malloc().

Sunday, June 24, 2018, by <EMAIL>
  Summary for 1.9.0 libpcap release
    Added testing system to libpcap, independent of tcpdump
    Changes to how pcap_t is activated
    Adding support for Large stream buffers on Endace DAG cards
    Changes to BSD 3-clause license to 2-clause license
    Additions to TCP header parsing, per RFC3168
    Add CMake build process (extensive number of changes)
    Assign a value for OpenBSD DLT_OPENFLOW.
    Support setting non-blocking mode before activating.
    Extensive build support for Windows VS2010 and MINGW (many many changes, over many months)
    Added RPCAPD support when --enable-remote (default no)
    Add the rpcap daemon source and build instructions.
    Put back the greasy "save the capture filter string so we can tweak it"
        hack, that keeps libpcap from capturing rpcap traffic.
    Fixes for captures on MacOS, utun0
    fixes so that non-AF_INET addresses, are not ==AF_INET6 addresses.
    Add a linktype for IBM SDLC frames containing SNA PDUs.
    pcap_compile() in 1.8.0 and later is newly thread-safe.
    bound snaplen for linux tpacket_v2 to ~64k
    Make VLAN filter handle both metadata and inline tags
    D-Bus captures can now be up to 128MB in size
    Added LORATAP DLT value
    Added DLT_VSOCK for https://qemu-project.org/Features/VirtioVsock
    probe_devices() fixes not to overrun buffer for name of device
    Add linux-specific pcap_set_protocol_linux() to allow specifying a specific capture protocol.
    RDMA sniffing support for pcap
    Add Nordic Semiconductor Bluetooth LE sniffer link-layer header type.
    fixes for reading /etc/ethers
    Make it possible to build on Windows without packet.dll.
    Add tests for large file support on UN*X.
    Solaris fixes to work with 2.8.6
    configuration test now looks for header files, not capture devices present
    Fix to work with Berkeley YACC.
    fixes for DragonBSD compilation of pcap-netmap.c
    Clean up the ether_hostton() stuff.
    Add an option to disable Linux memory-mapped capture support.
    Add DAG API support checks.
    Add Septel, Myricom SNF, and Riverbed TurboCap checks.
    Add checks for Linux USB, Linux Bluetooth, D-Bus, and RDMA sniffing support.
    Add a check for hardware time stamping on Linux.
    Don't bother supporting pre-2005 Visual Studio.
    Increased minimum autoconf version requirement to 2.64
    Add DLT value 273 for XRA-31 sniffer
    Clean up handing of signal interrupts in pcap_read_nocb_remote().
    Use the XPG 4.2 versions of the networking APIs in Solaris.
    Fix, and better explain, the "IPv6 means IPv6, not IPv4" option setting.
    Explicitly warn that negative packet buffer timeouts should not be used.
    rpcapd: Add support inetd-likes, including xinetd.conf, and systemd units
    Rename DLT_IEEE802_15_4 to DLT_IEEE802_15_4_WITHFCS.
    Add DISPLAYPORT AUX link type
    Remove the sunos4 kernel modules and all references to them.
    Add more interface flags to pcap_findalldevs().
  Summary for 1.9.0 libpcap release (to 2017-01-25 by <EMAIL>)
    Man page improvements
    Fix Linux cooked mode userspace filtering (GitHub pull request #429)
    Fix compilation if IPv6 support not enabled
    Fix some Linux memory-mapped capture buffer size issues
    Don't fail if kernel filter can't be set on Linux (GitHub issue
      #549)
    Improve sorting of interfaces for pcap_findalldevs()
    Don't list Linux usbmon devices if usbmon module isn't loaded
    Report PCAP_ERROR_PERM_DENIED if no permission to open Linux usbmon
      devices
    Fix DLT_ type for Solaris IPNET devices
    Always return an error message for errors finding DAG or Myricom
      devices
    If possible, don't require that a device be openable when
      enumerating them for pcap_findalldevs()
    Don't put incompletely-initialized addresses in the address list for
    When finding Myricom devices, update description for regular
      interfaces that are Myricom devices and handle SNF_FLAGS=0x2(port
      aggregation enabled)
    Fix compilation error in DAG support
    Fix issues with CMake configuration
    Add support for stream buffers larger than 2GB on newer DAG cards
    Remove support for building against DAG versions without STREAMS
      support (before dag-3.0.0 2007)

Tuesday, Oct. 25, 2016 <EMAIL>
  Summary for 1.8.1 libpcap release
    Add a target in Makefile.in for Exuberant Ctags use: 'extags'.
    Rename configure.in to configure.ac: autoconf 2.59
    Clean up the name-to-DLT mapping table.
    Add some newer DLT_ values: IPMI_HPM_2,ZWAVE_R1_R2,ZWAVE_R3,WATTSTOPPER_DLM,ISO_14443,RDS
    Clarify what the return values are for both success and failure.
    Many changes to build on windows
    Check for the "break the loop" condition in the inner loop for TPACKET_V3.
    Fix handling of packet count in the TPACKET_V3 inner loop: GitHub issue #493.
    Filter out duplicate looped back CAN frames.
    Fix the handling of loopback filters for IPv6 packets.
    Add a link-layer header type for RDS (IEC 62106) groups.
    Use different intermediate folders for x86 and x64 builds on Windows.
    On Linux, handle all CAN captures with pcap-linux.c, in cooked mode.
    Removes the need for the "host-endian" link-layer header type.
    Compile with '-Wused-but-marked-unused' in devel mode if supported
    Have separate DLTs for big-endian and host-endian SocketCAN headers.
    Reflect version.h being renamed to pcap_version.h.
    Require that version.h be generated: all build procedures we support generate version.h (autoconf, CMake, MSVC)!
    Properly check for sock_recv() errors.
    Re-impose some of Winsock's limitations on sock_recv().
    Replace sprintf() with pcap_snprintf().
    Fix signature of pcap_stats_ex_remote().
    Initial cmake support for remote packet capture.
    Have rpcap_remoteact_getsock() return a SOCKET and supply an "is active" flag.
    Clean up {DAG, Septel, Myricom SNF}-only builds.
    Do UTF-16-to-ASCII conversion into the right place.
    pcap_create_interface() needs the interface name on Linux.
    Clean up hardware time stamp support: the "any" device does not support any time stamp types.
    Add support for capturing on FreeBSD usbusN interfaces.
    Add a LINKTYPE/DLT_ value for FreeBSD USB.
    Go back to using PCAP_API on Windows.
    CMake support
    Add TurboCap support from WinPcap.
    Recognize 802.1ad nested VLAN tag in vlan filter.

Thursday Sep. 3, 2015 <EMAIL>
  Summary for 1.7.5 libpcap release
	Man page cleanups.
	Add some allocation failure checks.
	Fix a number of Linux/ucLinux configure/build issues.
	Fix some memory leaks.
	Recognize 802.1ad nested VLAN tag in vlan filter.
	Fix building Bluetooth Linux Monitor support with BlueZ 5.1+

Saturday Jun. 27, 2015 <EMAIL>
  Summary for 1.7.4 libpcap release
	Include fix for GitHub issue #424 -- out of tree builds.

Friday Apr. 10, 2015 <EMAIL>
  Summary for 1.7.3 libpcap release
	Work around a Linux bonding driver bug.

Thursday Feb. 12, 2015 <EMAIL>/<EMAIL>
  Summary for 1.7.2 libpcap release
	Support for filtering Geneve encapsulated packets.
	Generalize encapsulation handling, fixing some bugs.
	Don't add null addresses to address lists.
	Add pcap_dump_open_append() to open for appending.
	Fix the swapping of isochronous descriptors in Linux USB.
	Attempt to handle TPACKET_V1 with 32-bit userland and 64-bit kernel.

Wednesday Nov. 12, 2014 <EMAIL>/<EMAIL>
  Summary for 1.7.0 libpcap release
	Fix handling of zones for BPF on Solaris
	new DLT for ZWAVE
	clarifications for read timeouts.
	Use BPF extensions in compiled filters, fixing VLAN filters
	some fixes to compilation without stdint.h
	EBUSY can now be returned by SNFv3 code.
	Fix the range checks in BPF loads
	Various DAG fixes.
	Various Linux fixes.

Monday Aug. 12, 2014 <EMAIL>
  Summary for 1.6.2 libpcap release
	Don't crash on filters testing a nonexistent link-layer type
	    field.
	Fix sending in non-blocking mode on Linux with memory-mapped
	    capture.
	Fix timestamps when reading pcap-ng files on big-endian
	    machines.

Saturday  Jul. 19, 2014 <EMAIL>
  Summary for 1.6.1 libpcap release
	some fixes for the any device
	changes for how --enable-XXX (--enable-sniffing, --enable-can) works

Wednesday Jul. 2, 2014 <EMAIL>
  Summary for 1.6.0 libpcap release
        Don't support D-Bus sniffing on OS X
        fixes for byte order issues with NFLOG captures
        Handle using cooked mode for DLT_NETLINK in activate_new().
        on platforms where you can not capture on down interfaces, do not list them
        but: do list interfaces which are down, if you can capture on them!

Wednesday December 18, 2013 <EMAIL>
Summary for 1.5.3 libpcap release
	Don't let packets that don't match the current filter get to the
	    application when TPACKET_V3 is used. (GitHub issue #331)
	Fix handling of pcap_loop()/pcap_dispatch() with a packet count
	    of 0 on some platforms (including Linux with TPACKET_V3).
	    (GitHub issue #333)
	Work around TPACKET_V3 deficiency that causes packets to be lost
	    when a timeout of 0 is specified. (GitHub issue #335)
	Man page formatting fixes.

Wednesday December 4, 2013 <EMAIL>
Summary for 1.5.2 libpcap release
	Fix libpcap to work when compiled with TPACKET_V3 support and
	    running on a kernel without TPACKET_V3 support. (GitHub
	    issue #329)

Wednesday November 20, 2013 <EMAIL>
Summary for 1.5.1 libpcap release
	Report an error, rather than crashing, if an IPv6 address is
	    used for link-layer filtering.  (Wireshark bug 9376)

Wednesday October 30, 2013 <EMAIL>
Summary for 1.5.0 libpcap release
	TPACKET_V3 support added for Linux
	Point users to the the-tcpdump-group repository on GitHub rather
	    than the mcr repository
	Checks added for malloc()/realloc()/etc. failures
	Fixed build on Solaris 11
	Support filtering E1 SS7 traffic on MTP2 layer Annex A
	Use "ln -s" to link man pages by default
        Add support for getting nanosecond-resolution time stamps when
	    capturing and reading capture files
        Many changes to autoconf to deal better with non-GCC compilers
        added many new DLT types

Saturday April 6, 2013 <EMAIL>
Summary for 1.4.0 libpcap release
	Add netfilter/nfqueue interface.
	If we don't have support for IPv6 address resolution, support,
	    in filter expressions, what IPv6 stuff we can.
	Fix pcap-config to include -lpthread if canusb support is
	    present
	Try to fix "pcap_parse not defined" problems when --without-flex
	    and --without-bison are used when you have Flex and Bison
	Fix some issues with the pcap_loop man page.
	Fix pcap_getnonblock() and pcap_setnonblock() to fill in the
	    supplied error message buffer
	Fix typo that, it appeared, would cause pcap-libdlpi.c not to
	    compile (perhaps systems with libdlpi also have BPF and use
	    that instead)
	Catch attempts to call pcap_compile() on a non-activated pcap_t
	Fix crash on Linux with CAN-USB support without usbfs
	Fix addition of VLAN tags for Linux cooked captures
	Check for both EOPNOTSUPP and EINVAL after SIOCETHTOOL ioctl, so
	    that the driver can report either one if it doesn't support
	    SIOCETHTOOL
	Add DLT_INFINIBAND and DLT_SCTP
	Describe "proto XXX" and "protochain XXX" in the pcap-filter man
	    page
	Handle either directories, or symlinks to directories, that
	    correspond to interfaces in /sys/class/net
	Fix handling of VLAN tag insertion to check, on Linux 3.x
	    kernels, for VLAN tag valid flag
	Clean up some man pages
	Support libnl3 as well as libnl1 and libnl2 on Linux
	Fix handling of Bluetooth devices on 3.x Linux kernels

Friday  March 30, 2012.  <EMAIL>
Summary for 1.3.0 libpcap release
        Handle DLT_PFSYNC in {FreeBSD, other *BSD+Mac OS X, other}.
        Linux: Don't fail if netfilter isn't enabled in the kernel.
        Add new link-layer type for NFC Forum LLCP.
        Put the CANUSB stuff into EXTRA_DIST, so it shows up in the release tarball.
        Add LINKTYPE_NG40/DLT_NG40.
        Add DLT_MPEG_2_TS/LINKTYPE_MPEG_2_TS for MPEG-2 transport streams.
        [PATCH] Fix AIX-3.5 crash with read failure during stress
        AIX fixes.
        Introduce --disable-shared configure option.
        Added initial support for canusb devices.
        Include the pcap(3PCAP) additions as 1.2.1 changes.
        many updates to documentation: pcap.3pcap.in
        Improve 'inbound'/'outbound' capture filters under Linux.
        Note the cleanup of handling of new DLT_/LINKTYPE_ values.
        On Lion, don't build for PPC.
        For mac80211 devices we need to clean up monitor mode on exit.

Friday  December 9, 2011.  <EMAIL>.
Summary for 1.2.1 libpcap release
	Update README file.
	Fix typos in README.linux file.
	Clean up some compiler warnings.
	Fix Linux compile problems and tests for ethtool.h.
	Treat Debian/kFreeBSD and GNU/Hurd as systems with GNU
	 toolchains.
	Support 802.1 QinQ as a form of VLAN in filters.
	Treat "carp" as equivalent to "vrrp" in filters.
	Fix code generated for "ip6 protochain".
	Add some new link-layer header types.
	Support capturing NetFilter log messages on Linux.
	Clean up some error messages.
	Turn off monitor mode on exit for mac80211 interfaces on Linux.
	Fix problems turning monitor mode on for non-mac80211 interfaces
	 on Linux.
	Properly fail if /sys/class/net or /proc/net/dev exist but can't
	 be opened.
	Fail if pcap_activate() is called on an already-activated
	 pcap_t, and add a test program for that.
	Fix filtering in pcap-ng files.
	Don't build for PowerPC on Mac OS X Lion.
	Simplify handling of new DLT_/LINKTYPE_ values.
	Expand pcap(3PCAP) man page.

Sunday  July 24, 2011.  <EMAIL>.
Summary for 1.2 libpcap release
        All of the changes listed below for 1.1.1 and 1.1.2.
        Changes to error handling for pcap_findalldevs().
        Fix the calculation of the frame size in memory-mapped captures.
        Add a link-layer header type for STANAG 5066 D_PDUs.
        Add a link-layer type for a variant of 3GPP TS 27.010.
        Noted real nature of LINKTYPE_ARCNET.
        Add a link-layer type for DVB-CI.
        Fix configure-script discovery of VLAN acceleration support.
         see https://netoptimizer.blogspot.com/2010/09/tcpdump-vs-vlan-tags.html
        Linux, HP-UX, AIX, NetBSD and OpenBSD compilation/conflict fixes.
        Protect against including AIX 5.x's <net/bpf.h> having been included.
        Add DLT_DBUS, for raw D-Bus messages.
        Treat either EPERM or EACCES as "no soup for you".
        Changes to permissions on DLPI systems.
        Add DLT_IEEE802_15_4_NOFCS for 802.15.4 interfaces.

Fri.    August 6, 2010.  <EMAIL>.
Summary for 1.1.2 libpcap release
	Return DLT_ values, not raw LINKTYPE_ values from
	  pcap_datalink() when reading pcap-ng files
	Add support for "wlan ra" and "wlan ta", to check the RA and TA
	  of WLAN frames that have them
	Don't crash if "wlan addr{1,2,3,4}" are used without 802.11
	  headers
	Do filtering on USB and Bluetooth capturing
	On FreeBSD/SPARC64, use -fPIC - it's apparently necessary
	Check for valid port numbers (fit in a 16-bit unsigned field) in
	  "port" filters
	Reject attempts to put savefiles into non-blocking mode
	Check for "no such device" for the "get the media types" ioctl
	  in *BSD
	Improve error messages from bpf_open(), and let it do the error
	  handling
	Return more specific errors from pcap_can_set_rfmon(); fix
	  documentation
	Update description fetching code for FreeBSD, fix code for
	  OpenBSD
	Ignore /sys/net/dev files if we get ENODEV for them, not just
	  ENXIO; fixes handling of bonding devices on Linux
	Fix check for a constant 0 argument to BPF_DIV
	Use the right version of ar when cross-building
	Free any filter set on a savefile when the savefile is closed
	Include the CFLAGS setting when configure was run in the
	  compiler flags
	Add support for 802.15.4 interfaces on Linux

Thu.    April 1, 2010.  <EMAIL>.
Summary for 1.1.1 libpcap release
	Update CHANGES to reflect more of the changes in 1.1.0.
	Fix build on RHEL5.
	Fix shared library build on AIX.

Thu.	March 11, 2010.  <EMAIL>/<EMAIL>.
Summary for 1.1.0 libpcap release
	Add SocketCAN capture support
	Add Myricom SNF API support
	Update Endace DAG and ERF support
	Add support for shared libraries on Solaris, HP-UX, and AIX
	Build, install, and un-install shared libraries by default;
	  don't build/install shared libraries on platforms we don't support
	Fix building from a directory other than the source directory
	Fix compiler warnings and builds on some platforms
	Update config.guess and config.sub
	Support monitor mode on mac80211 devices on Linux
	Fix USB memory-mapped capturing on Linux; it requires a new DLT_
	  value
	On Linux, scan /sys/class/net for devices if we have it; scan
	  it, or /proc/net/dev if we don't have /sys/class/net, even if
	  we have getifaddrs(), as it'll find interfaces with no
	  addresses
	Add limited support for reading pcap-ng files
	Fix BPF driver-loading error handling on AIX
	Support getting the full-length interface description on FreeBSD
	In the lexical analyzer, free up any addrinfo structure we got back
	  from getaddrinfo().
	Add support for BPF and libdlpi in OpenSolaris (and SXCE)
	Hyphenate "link-layer" everywhere
	Add /sys/kernel/debug/usb/usbmon to the list of usbmon locations
	In pcap_read_linux_mmap(), if there are no frames available, call
	  poll() even if we're in non-blocking mode, so we pick up
	  errors, and check for the errors in question.
	Note that poll() works on BPF devices is Snow Leopard
	If an ENXIO or ENETDOWN is received, it may mean the device has
	  gone away.  Deal with it.
	For BPF, raise the default capture buffer size to from 32k to 512k
	Support ps_ifdrop on Linux
	Added a bunch of #ifdef directives to make wpcap.dll (WinPcap) compile
	 under cygwin.
	Changes to Linux mmapped captures.
	Fix bug where create_ring would fail for particular snaplen and
	  buffer size combinations
	Update pcap-config so that it handles libpcap requiring
	  additional libraries
	Add workaround for threadsafeness on Windows
	Add missing mapping for DLT_ENC <-> LINKTYPE_ENC
	DLT: Add DLT_CAN_SOCKETCAN
	DLT: Add Solaris ipnet
	Don't check for DLT_IPNET if it's not defined
	Add link-layer types for Fibre Channel FC-2
	Add link-layer types for Wireless HART
	Add link-layer types for AOS
	Add link-layer types for DECT
	Autoconf fixes (AIX, HP-UX, OSF/1, Tru64 cleanups)
	Install headers unconditionally, and include vlan.h/bluetooth.h if
	  enabled
	Autoconf fixes+cleanup
	Support enabling/disabling bluetooth (--{en,dis}able-bluetooth)
	Support disabling SITA support (--without-sita)
	Return -1 on failure to create packet ring (if supported but
	  creation failed)
	Fix handling of 'any' device, so that it can be opened, and no longer
	  attempt to open it in Monitor mode
	Add support for snapshot length for USB Memory-Mapped Interface
	Fix configure and build on recent Linux kernels
	Fix memory-mapped Linux capture to support pcap_next() and
	  pcap_next_ex()
	Fixes for Linux USB capture
	DLT: Add DLT_LINUX_EVDEV
	DLT: Add DLT_GSMTAP_UM
	DLT: Add DLT_GSMTAP_ABIS

Mon.    October 27, 2008.  <EMAIL>.  Summary for 1.0.0 libpcap release
	Compile with IPv6 support by default
	Compile with large file support on by default
	Add pcap-config script, which deals with -I/-L flags for compiling
	DLT: Add IPMB
	DLT: Add LAPD
	DLT: Add AX25 (AX.25 w/KISS header)
	DLT: Add JUNIPER_ST
	802.15.4 support
	Variable length 802.11 header support
	X2E data type support
	SITA ACN Interface support - see README.sita
	Support for memory-mapped capture on Linux
	Support for zerocopy BPF on platforms that support it
	Support for setting buffer size when opening devices
	Support for setting monitor mode when opening 802.11 devices
	Better support for dealing with VLAN tagging/stripping on Linux
	Fix dynamic library support on OSX
	Return PCAP_ERROR_IFACE_NOT_UP if the interface isn't 'UP', so applications
	 can print better diagnostic information
	Return PCAP_ERROR_PERM_DENIED if we don't have permission to open a device, so
	 applications can tell the user they need to go play with permissions
	On Linux, ignore ENETDOWN so we can continue to capture packets if the
	 interface goes down and comes back up again.
	On Linux, support new tpacket frame headers (2.6.27+)
	On Mac OS X, add scripts for changing permissions on /dev/bpf* and launchd plist
	On Solaris, support 'passive mode' on systems that support it
	Fixes to autoconf and general build environment
	Man page reorganization + cleanup
	Autogenerate VERSION numbers better

Mon.    September 10, 2007.  <EMAIL>.  Summary for 0.9.8 libpcap release
        Change build process to put public libpcap headers into pcap subdir
        DLT: Add value for IPMI IPMB packets
        DLT: Add value for u10 Networks boards
        Require <net/pfvar.h> for pf definitions - allows reading of pflog formatted
         libpcap files on an OS other than where the file was generated

Wed.	April 25, 2007.  <EMAIL>.  Summary for 0.9.6 libpcap release

	Put the public libpcap headers into a pcap subdirectory in both the
	 source directory and the target include directory, and have include
	 files at the top-level directory to include those headers, for
	 backwards compatibility.
	Add Bluetooth support
	Add USB capturing support on Linux
	Add support for the binary USB sniffing interface in Linux
	Add support for new FreeBSD BIOCSDIRECTION ioctl
	Add additional filter operations for 802.11 frame types
	Add support for filtering on MTP2 frame types
	Propagate some changes from the main branch, so the x.9 branch has
	 all the DLT_ and LINKTYPE_ values that the main branch does
	Reserved a DLT_ and SAVEFILE_ value for PPI (Per Packet Info)
	 encapsulated packets
	Add LINKTYPE_ for IEEE 802.15.4, with address fields padded as done
	 by Linux drivers
	Add LINKTYPE_ value corresponding to DLT_IEEE802_16_MAC_CPS.
	Add DLT for IEEE 802.16 (WiMAX) MAC Common Part Sublayer
	Add DLT for Bluetooth HCI UART transport layer
	When building a shared library, build with "-fPIC" on Linux to support x86_64
	Link with "$(CC) -shared" rather than "ld -shared" when building a
	 ".so" shared library
	Add support for autoconf 2.60
	Fixes to discard unread packets when changing filters
	Changes to handle name changes in the DAG library resulting from
	 switching to libtool.
	Add support for new DAG ERF types.
        Add an explicit "-ldag" when building the shared library, so the DAG
	 library dependency is explicit.
	Mac OSX fixes for dealing with "wlt" devices
	Fixes in add_or_find_if() & pcap_findalldevs() to optimize generating
	 device lists
	Fixed a bug in pcap_open_live(). The return value of PacketSetHwFilter
	 was not checked.

Tue.	September 19, 2006. <EMAIL>. Summary for 0.9.5 libpcap release

	Support for LAPD frames with vISDN
	Support for ERF on channelized T1/E1 cards via DAG API
	Fix capitalization that caused issues crossc compiling on Linux
	Better failure detection on PacketGetAdapterNames()
	Fixes for MPLS packet generation (link layer)
	OP_PACKET now matches the beginning of the packet, instead of
	 beginning+link-layer
	Add DLT/LINKTYPE for carrying FRF.16 Multi-link Frame Relay
	Fix allocation of buffer for list of link-layer types
	Added a new DLT and LINKTYPE value for ARINC 653 Interpartition Communication Messages
	Fixed a typo in a DLT value: it should start with DLT_ and not LINKTYPE_
	Redefined DLT_CAN20B and LINKTYPE_CAN20B as #190 (as this is the right value for CAN).
	Added definition for DLT_A429 and LINKTYPE_A429 as #184.
	Added a new DLT and LINKTYPE value for CAN v2.0B frames.
	Add support for DLT_JUNIPER_VP.
	Don't double-count received packets on Linux systems that
	 support the PACKET_STATISTICS getsockopt() argument on
	 PF_PACKET sockets.
	Add support for DLT_IEEE802_11 and DLT_IEEE802_11_RADIO link
	 layers in Windows
	Add support to build libpcap.lib and wpcap.dll under Cygnus and
	 MingW32.

Mon.	September 5, 2005.  <EMAIL>. Summary for 0.9.4 libpcap release

	Support for radiotap on Linux (Mike Kershaw)
	Fixes for HP-UX
	Support for additional Juniper link-layer types
	Fixes for filters on MPLS-encapsulated packets
	"vlan" filter fixed
	"pppoed" and "pppoes" filters added; the latter modifies later
	parts of the filter expression to look at the PPP headers and
	headers in the PPP payload

Tue.	July 5, 2005.  <EMAIL>. Summary for 0.9.3 libpcap release

	Fixes for compiling on nearly every platform,
		including improved 64bit support
	MSDOS Support
	Add support for sending packets
	OpenBSD pf format support
	IrDA capture (Linux only)

Tue.   March 30, 2004. <EMAIL>. Summary for 3.8.3 release

	Fixed minor problem in gencode.c that would appear on 64-bit
	platforms.
	Version number is now sane.

Mon.   March 29, 2004. <EMAIL>. Summary for 3.8.2 release

	updates for autoconf 2.5
	fixes for ppp interfaces for freebsd 4.1
	pcap gencode can generate code for 802.11, IEEE1394, and pflog.

Wed.   November 12, 2003. <EMAIL>. Summary for 0.8 release

	added pcap_findalldevs()
	Win32 patches from NetGroup, Politecnico di Torino (Italy)
	OpenBSD pf, DLT_PFLOG added
	Many changes to ATM support.
	lookup pcap_lookupnet()
	Added DLT_ARCNET_LINUX, DLT_ENC, DLT_IEEE802_11_RADIO, DLT_SUNATM,
		DLT_IP_OVER_FC, DLT_FRELAY, others.
	Sigh.  More AIX wonderfulness.
	Document updates.
	Changes to API: pcap_next_ex(), pcap_breakloop(), pcap_dump_flush(),
			pcap_list_datalinks(), pcap_set_datalink(),
			pcap_lib_version(), pcap_datalink_val_to_name(),
			pcap_datalink_name_to_val(), new error returns.

Tuesday, February 25, 2003. <EMAIL>.  0.7.2 release

	Support link types that use 802.2 always, never, and sometimes.
	Don't decrease the size of the BPF buffer from the default.
	Support frame relay.
	Handle 32-bit timestamps in DLPI, and pass the right buffer size.
	Handle Linux systems with modern kernel but without
	 SOL_PACKET in the userland headers.
	Linux support for ARPHRD_RAWHDLC.
	Handle 32-bit timestamps in snoop.
	Support eg (Octane/O2xxx/O3xxx Gigabit) devices.
	Add new reserved DLT types.

Monday October 23, 2001. <EMAIL>. Summary for 0.7 release

	Added pcap_findalldevs() call to get list of interfaces in a MI way.

	pcap_stats() has been documented as to what its counters mean on
	each platform.

Tuesday January 9, 2001. <EMAIL>. Summary for 0.6 release

	New Linux libpcap implementation, which, in 2.2 and later
	kernels, uses PF_PACKET sockets and supports kernel packet
	filtering (if compiled into the kernel), and supports the "any"
	device for capturing on all interfaces.  Cleans up promiscuous
	mode better on pre-2.2 kernels, and has various other fixes
	(handles 2.4 ARPHRD_IEEE802_TR, handles ISDN devices better,
	doesn't show duplicate packets on loopback interface, etc.).

	Fixed HP-UX libpcap implementation to correctly get the PPA for
	an interface, to allow interfaces to be opened by interface name.

	libpcap savefiles have system-independent link-layer type values
	in the header, rather than sometimes platform-dependent DLT_
	values, to make it easier to exchange capture files between
	different OSes.

	Non-standard capture files produced by some Linux tcpdumps, e.g.
	the one from Red Hat Linux 6.2 and later, can now be read.

	Updated autoconf stock files.

	Filter expressions can filter on VLAN IDs and various OSI
	protocols, and work on Token Ring (with non-source-routed
	packets).

	"pcap_open_dead()" added to allow compiling filter expressions
	to pcap code without opening a capture device or capture file.

	Header files fixed to allow use in C++ programs.

	Removed dependency on native headers for packet layout.
	Removed Linux specific headers that were shipped.

	Security fixes: Strcpy replaced with strlcpy, sprintf replaced
	with snprintf.

	Fixed bug that could cause subsequent "pcap_compile()"s to fail
	erroneously after one compile failed.

	Assorted other bug fixes.

	README.aix and README.linux files added to describe
	platform-specific issues.

	"getifaddrs()" rather than SIOCGIFCONF used, if available.

v0.5 Sat Jun 10 11:09:15 PDT 2000

<EMAIL>
- Brought in KAME IPv6/IPsec bpf compiler.
- Fixes for NetBSD.
- Support added for OpenBSD DLT_LOOP and BSD/OS DLT_C_HDLC (Cisco HDLC),
  and changes to work around different BSDs having different DLT_ types
  with the same numeric value.

Assar Westerlund  <<EMAIL>>
- Building outside the source code tree fixed.
- Changed to write out time stamps with 32-bit seconds and microseconds
  fields, regardless of whether those fields are 32 bits or 64 bits in
  the OS's native "struct timeval".
- Changed "pcap_lookupdev()" to dynamically grow the buffer into which
  the list of interfaces is read as necessary in order to hold the
  entire list.

Greg Troxel <<EMAIL>>
- Added a new "pcap_compile_nopcap()", which lets you compile a filter
  expression into a BPF program without having an open live capture or
  capture file.

v0.4 Sat Jul 25 12:40:09 PDT 1998

- Fix endian problem with DLT_NULL devices. From FreeBSD via Bill
  Fenner (<EMAIL>)

- Fix alignment problem with FDDI under DLPI. This was causing core
  dumps under Solaris.

- Added configure options to disable flex and bison. Resulted from a
  bug <NAME_EMAIL> (Bruce Barnett). Also added
  options to disable gcc and to force a particular packet capture type.

- Added support for Fore ATM interfaces (qaa and fa) under IRIX. Thanks
  to John Hawkinson (<EMAIL>)

- Change Linux PPP and SLIP to use DLT_RAW since the kernel does not
  supply any "link layer" data.

- Change Linux to use SIOCGIFHWADDR ioctl to determine link layer type.
  Thanks to Thomas Sailer (<EMAIL>)

- Change IRIX PPP to use DLT_RAW since the kernel does not supply any
  "link layer" data.

- Modified to support the new BSD/OS 2.1 PPP and SLIP link layer header
  formats.

- Added some new SGI snoop interface types. Thanks to Steve Alexander
  (<EMAIL>)

- Fixes for HP-UX 10.20 (which is similar to HP-UX 9). Thanks to
  Richard Allen (<EMAIL>) and Steinar Haug (<EMAIL>)

- Fddi supports broadcast as reported by Jeff Macdonald
  (<EMAIL>). Also correct ieee802 and arcnet.

- Determine Linux pcap buffer size at run time or else it might not be
  big enough for some interface types (e.g. FDDI). Thanks to Jes
  Sorensen (<EMAIL>)

- Fix some linux alignment problems.

- Document promisc argument to pcap_open_live(). Reported by Ian Marsh
  (<EMAIL>)

- Support Metricom radio packets under Linux. Thanks to Kevin Lai
  (<EMAIL>)

- Bind to interface name under Linux to avoid packets from multiple
  interfaces on multi-homed hosts. Thanks to Kevin Lai
  (<EMAIL>)

- Change L_SET to SEEK_SET for HP-UX. Thanks to Roland Roberts
  (<EMAIL>)

- Fixed an uninitialized memory reference found by Kent Vander Velden
  (<EMAIL>)

- Fixed lex pattern for IDs to allow leading digits. As reported by
  Theo de Raadt (<EMAIL>)

- Fixed Linux include file problems when using GNU libc.

- Ifdef ARPHRD_FDDI since not all versions of the Linux kernel have it.
  Reported reported by Eric Jacksch (<EMAIL>)

- Fixed bug in pcap_dispatch() that kept it from returning on packet
  timeouts.

- Changed ISLOOPBACK() macro when IFF_LOOPBACK isn't available to check
  for "lo" followed by an eos or digit (newer versions of Linux
  apparently call the loopback "lo" instead of "lo0").

- Fixed Linux networking include files to use ints instead of longs to
  avoid problems with 64 bit longs on the alpha. Thanks to Cristian
  Gafton (<EMAIL>)

v0.3 Sat Nov 30 20:56:27 PST 1996

- Added Linux support.

- Fixed savefile bugs.

- Solaris x86 fix from Tim Rylance (<EMAIL>)

- Add support for bpf kernel port filters.

- Remove duplicate atalk protocol table entry. Thanks to Christian
  Hopps (<EMAIL>)

- Fixed pcap_lookupdev() to ignore nonexistent devices. This was
  reported to happen under BSD/OS by David Vincenzetti
  (<EMAIL>)

- Avoid solaris compiler warnings. Thanks to Bruce Barnett
  (<EMAIL>)

v0.2.1 Sun Jul 14 03:02:26 PDT 1996

- Fixes for HP-UX 10. Thanks in part to Thomas Wolfram
  (<EMAIL>) and Rick Jones (<EMAIL>)

- Added support for SINIX. Thanks to Andrej Borsenkow
  (<EMAIL>)

- Fixes for AIX (although this system is not yet supported). Thanks to
  John Hawkinson (<EMAIL>)

- Use autoconf's idea of the top level directory in install targets.
  Thanks to John Hawkinson.

- Add missing autoconf packet capture result message. Thanks to Bill
  Fenner (<EMAIL>)

- Fixed padding problems in the pf module.

- Fixed some more alignment problems on the alpha.

- Added explicit netmask support. Thanks to Steve Nuchia
  (<EMAIL>)

- Fixed to handle raw ip addresses such as ******* without "left
  justifying"

- Add "sca" keyword (for DEC cluster services) as suggested by Terry
  Kennedy (<EMAIL>)

- Add "atalk" keyword as suggested by John Hawkinson.

- Add "igrp" keyword.

- Fixed HID definition in grammar.y to be a string, not a value.

- Use $CC when checking gcc version. Thanks to Carl Lindberg
  (<EMAIL>)

- Removed obsolete reference to pcap_immediate() from the man page.
  Michael Stolarchuk (<EMAIL>)

- DLT_NULL has a 4 byte family header. Thanks to Jeffrey Honig
  (<EMAIL>)

v0.2 Sun Jun 23 02:28:42 PDT 1996

- Add support for HP-UX. Resulted from code contributed by Tom Murray
  (<EMAIL>) and Philippe-Andri Prindeville
  (<EMAIL>)

- Update INSTALL with a reminder to install include files. Thanks to
  Mark Andrews (<EMAIL>)

- Fix bpf compiler alignment bug on the alpha.

- Use autoconf to detect architectures that can't handle misaligned
  accesses.

- Added loopback support for snoop. Resulted from report Steve
  Alexander (<EMAIL>)

v0.1 Fri Apr 28 18:11:03 PDT 1995

- Fixed compiler and optimizer bugs.  The BPF filter engine uses unsigned
  comparison operators, while the code generator and optimizer assumed
  signed semantics in several places.  Thanks to Charlie Slater
  (<EMAIL>) for pointing this out.

- Removed FDDI ifdef's, they aren't really needed. Resulted from report
  by Gary Veum (<EMAIL>).

- Add pcap-null.c which allows offline use of libpcap on systems that
  don't support live package capture. This feature resulting from a
  request from Jan van Oorschot (<EMAIL>).

- Make bpf_compile() reentrant. Fix thanks to Pascal Hennequin
  (<EMAIL>).

- Port to GNU autoconf.

- Fix pcap-dlpi.c to work with isdn. Resulted from report by Flemming
  Johansen (<EMAIL>).

- Handle multi-digit interface unit numbers (aka ppa's) under dlpi.
  Resulted from report by Daniel Ehrlich (<EMAIL>).

- Fix pcap-dlpi.c to work in non-promiscuous mode. Resulted from report
  by Jeff Murphy (<EMAIL>).

- Add support for "long jumps". Thanks to Jeffrey Mogul
  (<EMAIL>).

- Fix minor problems when compiling with BDEBUG as noticed by Scott
  Bertilson (<EMAIL>).

- Declare sys_errlist "const char *const" to avoid problems under
  FreeBSD. Resulted from <NAME_EMAIL>.

v0.0.6 Fri Apr 28 04:07:13 PDT 1995

- Add missing variable declaration missing from 0.0.6

v0.0.5 Fri Apr 28 00:22:21 PDT 1995

- Workaround for problems when pcap_read() returns 0 due to the timeout
  expiring.

v0.0.4 Thu Apr 20 20:41:48 PDT 1995

- Change configuration to not use gcc v2 flags with gcc v1.

- Fixed a bug in pcap_next(); if pcap_dispatch() returns 0, pcap_next()
  should also return 0. Thanks to Richard Stevens (<EMAIL>).

- Fixed configure to test for snoop before dlpi to avoid problems under
  IRIX 5. Thanks to J. Eric Townsend (<EMAIL>).

- Hack around deficiency in Ultrix's make.

- Fix two bugs related to the Solaris pre-5.3.2 bufmod bug; handle
  savefiles that have more than snapshot bytes of data in them (so we
  can read old savefiles) and avoid writing such files.

- Added checkioctl which is used with gcc to check that the
  "fixincludes" script has been run.

v0.0.3 Tue Oct 18 18:13:46 PDT 1994

- Fixed configure to test for snoop before dlpi to avoid problems under
  IRIX 5. Thanks to J. Eric Townsend (<EMAIL>).

v0.0.2 Wed Oct 12 20:56:37 PDT 1994

- Implement timeout in the dlpi pcap_open_live(). Thanks to Richard
  Stevens.

- Determine pcap link type from dlpi media type. Resulted from report
  by Mahesh Jethanandani (<EMAIL>).

v0.0.1 Fri Jun 24 14:50:57 PDT 1994

- Fixed bug in nit_setflags() in pcap-snit.c. The streams ioctl timeout
  wasn't being initialized sometimes resulting in an "NIOCSFLAGS:
  Invalid argument" error under OSF/1. Reported by Matt Day
  (<EMAIL>) and Danny Mitzel (<EMAIL>).

- Turn on FDDI support by default.

v0.0 Mon Jun 20 19:20:16 PDT 1994

- Initial release.

- Fixed bug with greater/less keywords, reported by Mark Andrews
  (<EMAIL>).

- Fix bug where '|' was defined as BPF_AND instead of BPF_OR, reported
  by Elan Amir (<EMAIL>).

- Machines with little-endian byte ordering are supported thanks to
  Jeff Mogul.

- Add hack for version 2.3 savefiles which don't have caplen and len
  swapped thanks to Vern Paxson.

- Added "&&" and "||" aliases for "and" and "or" thanks to Vern Paxson.

- Added length, inbound and outbound keywords.
