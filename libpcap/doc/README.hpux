If you use HP-UX, you must have at least version 10.20 and either the
version of `cc` that supports C99 (`cc -AC99`) or else use the GNU C
compiler.  The required DLPI streams package is standard starting with
HP-UX 10.

The HP implementation of DLPI is a little bit eccentric. Unlike
Solaris, you must attach `/dev/dlpi` instead of the specific `/dev/*`
network pseudo device entry in order to capture packets. The PPA is
based on the ifnet "index" number.  Under HP-UX 10,
DLPI can provide information for determining the PPA. It does not seem
to be possible to trace the loopback interface. Unlike other DLPI
implementations, PHYS implies MULTI and SAP and you get an error if you
try to enable more than one promiscuous mode at a time.

To capture outbound packets on HP-UX 10, you will, apparently, need a
late "LAN products cumulative
patch" (at one point, it was claimed that this would be PHNE_18173 for
s700/10.20; at another point, it was claimed that the required patches
were PHNE_20892, PHNE_20725 and PHCO_10947, or newer patches), and to do
so on HP-UX 11 you will, apparently, need the latest lancommon/DLPI
patches and the latest driver patch for the interface(s) in use on HP-UX
11 (at one point, it was claimed that patches PHNE_19766, PHNE_19826,
PHNE_20008, and PHNE_20735 did the trick).

Furthermore, on HP-UX 10, you will need to turn on a kernel switch by
doing

	echo 'lanc_outbound_promisc_flag/W 1' | adb -w /stand/vmunix /dev/mem

You would have to arrange that this happens on reboots; the right way to
do that would probably be to put it into an executable script file
`/sbin/init.d/outbound_promisc` and making
`/sbin/rc2.d/S350outbound_promisc` a symbolic link to that script.

Finally, testing shows that there can't be more than one simultaneous
DLPI user per network interface.

For HP-UX 11i (11.11) and later, there are no known issues with
promiscuous mode under HP-UX.  If you are using a earlier version of
HP-UX and cannot upgrade, please continue reading.

HP-UX patches to fix packet capture problems

Note that packet-capture programs such as tcpdump may, on HP-UX, not be
able to see packets sent from the machine on which they're running.
Some articles on groups.google.com discussing this are:

	https://groups.google.com/groups?selm=82ld3v%2480i%241%40mamenchi.zrz.TU-Berlin.DE

which says:

  Newsgroups: comp.sys.hp.hpux
  Subject:  Re: Did someone made tcpdump working on 10.20 ?
  Date: 12/08/1999
  From: Lutz Jaenicke <<EMAIL>>

  In article <82ks5i$5vc$<EMAIL>>, mtsat <<EMAIL>>
  wrote:
   >Hello,
   >
   >I downloaded and compiled tcpdump3.4 a couple of week ago. I tried to use
   >it, but I can only see incoming data, never outgoing.
   >Someone (raj) explained me that a patch was missing, and that this patch
   >must me "patched" (poked) in order to see outbound data in promiscuous mode.
   >Many things to do .... So the question is : did someone has already this
   >"ready to use" PHNE_**** patch ?

   Two things:
   1. You do need a late "LAN products cumulative patch" (e.g.  PHNE_18173
  for   s700/10.20).
   2. You must use
echo 'lanc_outbound_promisc_flag/W1' | /usr/bin/adb -w /stand/vmunix /dev/kmem
     You can insert this e.g. into /sbin/init.d/lan

   Best regards,
   Lutz

and

	http://groups.google.com/groups?selm=88cf4t%24p03%241%40web1.cup.hp.com

which says:

  Newsgroups: comp.sys.hp.hpux
  Subject: Re: tcpdump only shows incoming packets
  Date: 02/15/2000
  From: Rick Jones <<EMAIL>>

  Harald Skotnes <<EMAIL>> wrote:
  > I am running HPUX 11.0 on a C200 hanging on a 100Mb switch. I have
  > compiled libpcap-0.4 an tcpdump-3.4 and it seems to work. But at a
  > closer look I only get to see the incoming packets not the
  > outgoing. I have tried tcpflow-0.12 which also uses libpcap and the
  > same thing happens.  Could someone please give me a hint on how to
  > get this right?

  Search/Read the archives ?-)

  What you are seeing is expected, un-patched, behaviour for an HP-UX
  system.  On 11.00, you need to install the latest lancommon/DLPI
  patches, and then the latest driver patch for the interface(s) in use.
  At that point, a miracle happens and you should start seeing outbound
  traffic.

[That article also mentions the patch that appears below.]

and

	https://groups.google.com/groups?selm=38AA973E.96BE7DF7%40cc.uit.no

which says:

  Newsgroups: comp.sys.hp.hpux
  Subject: Re: tcpdump only shows incoming packets
  Date: 02/16/2000
  From: Harald Skotnes <<EMAIL>>

  Rick Jones wrote:

	...

  > What you are seeing is expected, un-patched, behaviour for an HP-UX
  > system. On 11.00, you need to install the latest lancommon/DLPI
  > patches, and then the latest driver patch for the interface(s) in
  > use. At that point, a miracle happens and you should start seeing
  > outbound traffic.

  Thanks a lot.  I have this problem on several machines running HPUX
  10.20 and 11.00.  The machines where patched up before y2k so did not
  know what to think.  Anyway I have now installed PHNE_19766,
  PHNE_19826, PHNE_20008, PHNE_20735 on the C200 and now I can see the
  outbound traffic too.  Thanks again.

(although those patches may not be the ones to install - there may be
later patches).

And another <NAME_EMAIL>, from Rick Jones:

  Date: Mon, 29 Apr 2002 15:59:55 -0700
  From: Rick Jones
  To: <EMAIL>
  Subject: Re: [tcpdump-workers] I Can't Capture the Outbound Traffic

	...

  http://itrc.hp.com/ would be one place to start in a search for the most
  up-to-date patches for DLPI and the lan driver(s) used on your system (I
  cannot guess because 9000/800 is too generic - one hs to use the "model"
  command these days and/or an ioscan command (see manpage) to guess what
  the drivers (btlan[3456], gelan, etc) might be involved in addition to
  DLPI.

  Another option is to upgrade to 11i as outbound promiscuous mode support
  is there in the base OS, no patches required.

Rick Jones reports that HP-UX 11i needs no patches for outbound
promiscuous mode support.

An additional note, from Jost Martin, for HP-UX 10.20:

	Q: How do I get [Wireshark] on HPUX to capture the _outgoing_ packets
	   of an interface
	A: You need to get PHNE_20892,PHNE_20725 and PHCO_10947 (or
	   newer, this is as of 4.4.00) and its dependencies.  Then you can
	   enable the feature as described below:

	Patch Name: PHNE_20892
	Patch Description: s700 10.20 PCI 100Base-T cumulative patch
		To trace the outbound packets, please do the following
		to turn on a global promiscuous switch before running
		the promiscuous applications like snoop or tcpdump:

		adb -w /stand/vmunix /dev/mem
		lanc_outbound_promisc_flag/W 1
		(adb will echo the result showing that the flag has
		been changed)
		$quit
	(Thanks for this part to HP-support, Ratingen)

		The attached hack does this and some security-related stuff
	(<NAME_EMAIL> (Ralf Hildebrandt) who
	posted the security-part some time ago)

		 <<hack_ip_stack>>

		(Don't switch IP-forwarding off, if you need it !)
		Install the hack as /sbin/init.d/hacl_ip_stack (adjust
	permissions !) and make a sequencing-symlink
	/sbin/rc2.d/S350hack_ip_stack pointing to this script.
		Now all this is done on every reboot.

According to Rick Jones, the global promiscuous switch also has to be
turned on for HP-UX 11.00, but not for 11i - and, in fact, the switch
doesn't even exist on 11i.

Here's the "hack_ip_stack" script:

-----------------------------------Cut Here-------------------------------------
#!/sbin/sh
#
# nettune:  hack kernel params for safety

OKAY=0
ERROR=-1

# /usr/contrib/bin fuer nettune auf Pfad
PATH=/sbin:/usr/sbin:/usr/bin:/usr/contrib/bin
export PATH


##########
#  main  #
##########

case $1 in
   start_msg)
      print "Tune IP-Stack for security"
      exit $OKAY
      ;;

   stop_msg)
      print "This action is not applicable"
      exit $OKAY
      ;;

   stop)
      exit $OKAY
      ;;

   start)
      ;;  # fall through

   *)
      print "USAGE: $0 {start_msg | stop_msg | start | stop}" >&2
      exit $ERROR
      ;;
   esac

###########
#  start  #
###########

#
# tcp-Sequence-Numbers nicht mehr inkrementieren sondern random
# Syn-Flood-Protection an
# ip_forwarding aus
# Source-Routing aus
# Ausgehende Packets an ethereal/tcpdump etc.

/usr/contrib/bin/nettune -s tcp_random_seq 2 || exit $ERROR
/usr/contrib/bin/nettune -s hp_syn_protect 1 || exit $ERROR
/usr/contrib/bin/nettune -s ip_forwarding 0 || exit $ERROR
echo 'ip_block_source_routed/W1' | /usr/bin/adb -w /stand/vmunix /dev/kmem || exit $ERROR
echo 'lanc_outbound_promisc_flag/W 1' | adb -w /stand/vmunix /dev/mem  || exit $ERROR

exit $OKAY
-----------------------------------Cut Here-------------------------------------

If you are trying to do packet capture with a FORE ATM card, you may or
may not be able to. They usually only release their driver in object
code so unless their driver supports packet capture, there's not much
libpcap can do.

If you get an error like:

    tcpdump: recv_ack: bind error 0x???

when using DLPI, look for the DL_ERROR_ACK error return values, usually
in `/usr/include/sys/dlpi.h`, and find the corresponding value.
