[{"arguments": ["/usr/bin/gcc", "-g", "-O0", "-DDEBUG", "-fpic", "-I.", "-DBUILDING_PCAP", "-Dpcap_EXPORTS", "-DHAVE_CONFIG_H", "-g", "-O0", "-DDEBUG", "-c", "-o", "pcap-linux.o", "pcap-linux.c"], "directory": "/app/tcpdump/libpcap", "file": "/app/tcpdump/libpcap/pcap-linux.c", "output": "/app/tcpdump/libpcap/pcap-linux.o"}, {"arguments": ["/usr/bin/gcc", "-g", "-O0", "-DDEBUG", "-fpic", "-I.", "-DBUILDING_PCAP", "-Dpcap_EXPORTS", "-DHAVE_CONFIG_H", "-g", "-O0", "-DDEBUG", "-c", "-o", "fad-getad.o", "fad-getad.c"], "directory": "/app/tcpdump/libpcap", "file": "/app/tcpdump/libpcap/fad-getad.c", "output": "/app/tcpdump/libpcap/fad-getad.o"}, {"arguments": ["/usr/bin/gcc", "-g", "-O0", "-DDEBUG", "-fpic", "-I.", "-DBUILDING_PCAP", "-Dpcap_EXPORTS", "-DHAVE_CONFIG_H", "-g", "-O0", "-DDEBUG", "-c", "-o", "pcap-usb-linux.o", "pcap-usb-linux.c"], "directory": "/app/tcpdump/libpcap", "file": "/app/tcpdump/libpcap/pcap-usb-linux.c", "output": "/app/tcpdump/libpcap/pcap-usb-linux.o"}, {"arguments": ["/usr/bin/gcc", "-g", "-O0", "-DDEBUG", "-fpic", "-I.", "-DBUILDING_PCAP", "-Dpcap_EXPORTS", "-DHAVE_CONFIG_H", "-g", "-O0", "-DDEBUG", "-c", "-o", "pcap-netfilter-linux.o", "pcap-netfilter-linux.c"], "directory": "/app/tcpdump/libpcap", "file": "/app/tcpdump/libpcap/pcap-netfilter-linux.c", "output": "/app/tcpdump/libpcap/pcap-netfilter-linux.o"}, {"arguments": ["/usr/bin/gcc", "-g", "-O0", "-DDEBUG", "-fpic", "-I.", "-DBUILDING_PCAP", "-Dpcap_EXPORTS", "-DHAVE_CONFIG_H", "-g", "-O0", "-DDEBUG", "-c", "-o", "pcap.o", "pcap.c"], "directory": "/app/tcpdump/libpcap", "file": "/app/tcpdump/libpcap/pcap.c", "output": "/app/tcpdump/libpcap/pcap.o"}, {"arguments": ["/usr/bin/gcc", "-g", "-O0", "-DDEBUG", "-fpic", "-I.", "-DBUILDING_PCAP", "-Dpcap_EXPORTS", "-DHAVE_CONFIG_H", "-g", "-O0", "-DDEBUG", "-c", "./gencode.c"], "directory": "/app/tcpdump/libpcap", "file": "/app/tcpdump/libpcap/gencode.c"}, {"arguments": ["/usr/bin/gcc", "-g", "-O0", "-DDEBUG", "-fpic", "-I.", "-DBUILDING_PCAP", "-Dpcap_EXPORTS", "-DHAVE_CONFIG_H", "-g", "-O0", "-DDEBUG", "-c", "-o", "optimize.o", "optimize.c"], "directory": "/app/tcpdump/libpcap", "file": "/app/tcpdump/libpcap/optimize.c", "output": "/app/tcpdump/libpcap/optimize.o"}, {"arguments": ["/usr/bin/gcc", "-g", "-O0", "-DDEBUG", "-fpic", "-I.", "-DBUILDING_PCAP", "-Dpcap_EXPORTS", "-DHAVE_CONFIG_H", "-g", "-O0", "-DDEBUG", "-c", "-o", "nametoaddr.o", "nametoaddr.c"], "directory": "/app/tcpdump/libpcap", "file": "/app/tcpdump/libpcap/nametoaddr.c", "output": "/app/tcpdump/libpcap/nametoaddr.o"}, {"arguments": ["/usr/bin/gcc", "-g", "-O0", "-DDEBUG", "-fpic", "-I.", "-DBUILDING_PCAP", "-Dpcap_EXPORTS", "-DHAVE_CONFIG_H", "-g", "-O0", "-DDEBUG", "-c", "-o", "etherent.o", "etherent.c"], "directory": "/app/tcpdump/libpcap", "file": "/app/tcpdump/libpcap/etherent.c", "output": "/app/tcpdump/libpcap/etherent.o"}, {"arguments": ["/usr/bin/gcc", "-g", "-O0", "-DDEBUG", "-fpic", "-I.", "-DBUILDING_PCAP", "-Dpcap_EXPORTS", "-DHAVE_CONFIG_H", "-g", "-O0", "-DDEBUG", "-c", "-o", "fmtutils.o", "fmtutils.c"], "directory": "/app/tcpdump/libpcap", "file": "/app/tcpdump/libpcap/fmtutils.c", "output": "/app/tcpdump/libpcap/fmtutils.o"}, {"arguments": ["/usr/bin/gcc", "-g", "-O0", "-DDEBUG", "-fpic", "-I.", "-DBUILDING_PCAP", "-Dpcap_EXPORTS", "-DHAVE_CONFIG_H", "-g", "-O0", "-DDEBUG", "-c", "-o", "pcap-util.o", "pcap-util.c"], "directory": "/app/tcpdump/libpcap", "file": "/app/tcpdump/libpcap/pcap-util.c", "output": "/app/tcpdump/libpcap/pcap-util.o"}, {"arguments": ["/usr/bin/gcc", "-g", "-O0", "-DDEBUG", "-fpic", "-I.", "-DBUILDING_PCAP", "-Dpcap_EXPORTS", "-DHAVE_CONFIG_H", "-g", "-O0", "-DDEBUG", "-c", "-o", "savefile.o", "savefile.c"], "directory": "/app/tcpdump/libpcap", "file": "/app/tcpdump/libpcap/savefile.c", "output": "/app/tcpdump/libpcap/savefile.o"}, {"arguments": ["/usr/bin/gcc", "-g", "-O0", "-DDEBUG", "-fpic", "-I.", "-DBUILDING_PCAP", "-Dpcap_EXPORTS", "-DHAVE_CONFIG_H", "-g", "-O0", "-DDEBUG", "-c", "-o", "sf-pcap.o", "sf-pcap.c"], "directory": "/app/tcpdump/libpcap", "file": "/app/tcpdump/libpcap/sf-pcap.c", "output": "/app/tcpdump/libpcap/sf-pcap.o"}, {"arguments": ["/usr/bin/gcc", "-g", "-O0", "-DDEBUG", "-fpic", "-I.", "-DBUILDING_PCAP", "-Dpcap_EXPORTS", "-DHAVE_CONFIG_H", "-g", "-O0", "-DDEBUG", "-c", "-o", "sf-pcapng.o", "sf-pcapng.c"], "directory": "/app/tcpdump/libpcap", "file": "/app/tcpdump/libpcap/sf-pcapng.c", "output": "/app/tcpdump/libpcap/sf-pcapng.o"}, {"arguments": ["/usr/bin/gcc", "-g", "-O0", "-DDEBUG", "-fpic", "-I.", "-DBUILDING_PCAP", "-Dpcap_EXPORTS", "-DHAVE_CONFIG_H", "-g", "-O0", "-DDEBUG", "-c", "-o", "pcap-common.o", "pcap-common.c"], "directory": "/app/tcpdump/libpcap", "file": "/app/tcpdump/libpcap/pcap-common.c", "output": "/app/tcpdump/libpcap/pcap-common.o"}, {"arguments": ["/usr/bin/gcc", "-g", "-O0", "-DDEBUG", "-fpic", "-I.", "-DBUILDING_PCAP", "-Dpcap_EXPORTS", "-DHAVE_CONFIG_H", "-g", "-O0", "-DDEBUG", "-c", "-o", "bpf_image.o", "bpf_image.c"], "directory": "/app/tcpdump/libpcap", "file": "/app/tcpdump/libpcap/bpf_image.c", "output": "/app/tcpdump/libpcap/bpf_image.o"}, {"arguments": ["/usr/bin/gcc", "-g", "-O0", "-DDEBUG", "-fpic", "-I.", "-DBUILDING_PCAP", "-Dpcap_EXPORTS", "-DHAVE_CONFIG_H", "-g", "-O0", "-DDEBUG", "-c", "-o", "bpf_filter.o", "bpf_filter.c"], "directory": "/app/tcpdump/libpcap", "file": "/app/tcpdump/libpcap/bpf_filter.c", "output": "/app/tcpdump/libpcap/bpf_filter.o"}, {"arguments": ["/usr/bin/gcc", "-g", "-O0", "-DDEBUG", "-fpic", "-I.", "-DBUILDING_PCAP", "-Dpcap_EXPORTS", "-DHAVE_CONFIG_H", "-g", "-O0", "-DDEBUG", "-c", "-o", "bpf_dump.o", "bpf_dump.c"], "directory": "/app/tcpdump/libpcap", "file": "/app/tcpdump/libpcap/bpf_dump.c", "output": "/app/tcpdump/libpcap/bpf_dump.o"}, {"arguments": ["/usr/bin/gcc", "-g", "-O0", "-DDEBUG", "-fpic", "-I.", "-DBUILDING_PCAP", "-Dpcap_EXPORTS", "-DHAVE_CONFIG_H", "-g", "-O0", "-DDEBUG", "-c", "scanner.c"], "directory": "/app/tcpdump/libpcap", "file": "/app/tcpdump/libpcap/scanner.c"}, {"arguments": ["/usr/bin/gcc", "-g", "-O0", "-DDEBUG", "-fpic", "-I.", "-DBUILDING_PCAP", "-Dpcap_EXPORTS", "-DHAVE_CONFIG_H", "-g", "-O0", "-DDEBUG", "-c", "grammar.c"], "directory": "/app/tcpdump/libpcap", "file": "/app/tcpdump/libpcap/grammar.c"}, {"arguments": ["/usr/bin/gcc", "-g", "-O0", "-DDEBUG", "-fpic", "-I.", "-DBUILDING_PCAP", "-Dpcap_EXPORTS", "-DHAVE_CONFIG_H", "-g", "-O0", "-DDEBUG", "-c", "-o", "strlcat.o", "./missing/strlcat.c"], "directory": "/app/tcpdump/libpcap", "file": "/app/tcpdump/libpcap/missing/strlcat.c", "output": "/app/tcpdump/libpcap/strlcat.o"}, {"arguments": ["/usr/bin/gcc", "-g", "-O0", "-DDEBUG", "-fpic", "-I.", "-DBUILDING_PCAP", "-Dpcap_EXPORTS", "-DHAVE_CONFIG_H", "-g", "-O0", "-DDEBUG", "-c", "-o", "strlcpy.o", "./missing/strlcpy.c"], "directory": "/app/tcpdump/libpcap", "file": "/app/tcpdump/libpcap/missing/strlcpy.c", "output": "/app/tcpdump/libpcap/strlcpy.o"}]