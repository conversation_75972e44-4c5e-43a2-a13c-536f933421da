.\" Copyright (c) 1994, 1996, 1997
.\"	The Regents of the University of California.  All rights reserved.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that: (1) source code distributions
.\" retain the above copyright notice and this paragraph in its entirety, (2)
.\" distributions including binary code include the above copyright notice and
.\" this paragraph in its entirety in the documentation or other materials
.\" provided with the distribution, and (3) all advertising materials mentioning
.\" features or use of this software display the following acknowledgement:
.\" ``This product includes software developed by the University of California,
.\" Lawrence Berkeley Laboratory and its contributors.'' Neither the name of
.\" the University nor the names of its contributors may be used to endorse
.\" or promote products derived from this software without specific prior
.\" written permission.
.\" THIS SOFTWARE IS PROVIDED ``AS IS'' AND WITHOUT ANY EXPRESS OR IMPLIED
.\" WARRANTIES, INCLUDING, WITHOUT LIMITATION, THE IMPLIED WARRANTIES OF
.\" MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
.\"
.TH RPCAPD-CONFIG @MAN_FILE_FORMATS@ "6 January 2019"
.SH NAME
rpcapd-config \- rpcapd configuration file format
.SH DESCRIPTION
An
.I rpcapd
configuration file allows parameters to be set for
.BR rpcapd (@MAN_ADMIN_COMMANDS@).
.LP
A
.B #
introduces a comment that runs to the end of the line.  Blank lines,
and lines with only a comment, are ignored.  Leading and trailing white
space on a line are also ignored.
.LP
Lines that set a parameter are of the form
.IP
\fIparameter\fB=\fIvalue\fR
.LP
Whitespace preceding or following the
.B =
is ignored.
.LP
The
.IR parameter s
are:
.TP
.B ActiveClient
.I value
is a host name or IP address, followed by a comma,
semicolon, or space, followed by a port name and address or
.BR DEFAULT .
.B DEFAULT
specifies the default active mode port for rpcapd, port 2003.
Each
.B ActiveClient
line adds the host and port to the list of clients to which the server
should connect in active mode.
.TP
.B PassiveClient
.I value
is a host name or IP address, followed by a comma,
semicolon, or space, followed by a port name and address or
.BR DEFAULT .
.B DEFAULT
specifies the default passive mode port for rpcapd, port 2002.
Each
.B PassiveClient
line adds the host and port to the list of clients addresses and ports
that are allowed to connect to the server in passive mode.
.TP
.B NullAuthPermit
.I value
is either
.B YES
or
.BR NO .
.B YES
means that null authentication is permitted;
.B NO
means that it is not permitted.
.SH SEE ALSO
.BR rpcapd (@MAN_ADMIN_COMMANDS@)
