#include "config.h"
#undef PACKAGE_NAME
#include <winver.h>
#include <rpcapd.h>
#define PACKAGE_NAME PROGRAM_NAME

  VS_VERSION_INFO VERSIONINFO
    FILEVERSION    PACKAGE_VERSION_DLL
    PRODUCTVERSION PACKAGE_VERSION_DLL
    FILEFLAGSMASK  0x3fL
    FILEOS         VOS__WINDOWS32
    FILETYPE       VFT_APP
#ifdef _DEBUG
    FILEFLAGS 0x1L
#else
    FILEFLAGS 0x0L
#endif
  BEGIN
    BLOCK "StringFileInfo"
    BEGIN
      BLOCK "040904b0"
      BEGIN
        VALUE "Comments",         "https://github.com/the-tcpdump-group/libpcap/"
        VALUE "CompanyName",      "The TCPdump Group"
        VALUE "FileDescription",  "Remote Packet Capture Daemon"
        VALUE "FileVersion",      "PACKAGE_VERSION_DLL"
        VALUE "InternalName",     PACKA<PERSON>_NAME
        VALUE "LegalCopyright",   "Copyright (c) The TCPdump Group"
        VALUE "LegalTrademarks",  ""
        VALUE "OriginalFilename", "rpcapd.exe"
        VALUE "ProductName",      PACKAGE_NAME
        VALUE "ProductVersion",   PACKAGE_VERSION
      END
    END
  BLOCK "VarFileInfo"
  BEGIN
    VALUE "Translation", 0x0, 1200
  END
  END
