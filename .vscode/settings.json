{"C_Cpp.intelliSenseEngine": "default", "C_Cpp.intelliSenseEngineFallback": "enabled", "C_Cpp.default.compileCommands": "${workspaceFolder}/compile_commands.json", "C_Cpp.default.cStandard": "c11", "C_Cpp.default.intelliSenseMode": "linux-gcc-x64", "C_Cpp.errorSquiggles": "enabled", "C_Cpp.autoAddFileAssociations": false, "C_Cpp.clang_format_fallbackStyle": "{ BasedOnStyle: LLVM, IndentWidth: 4, TabWidth: 4, UseTab: Always }", "files.associations": {"*.h": "c", "*.c": "c"}, "C_Cpp.loggingLevel": "Information"}