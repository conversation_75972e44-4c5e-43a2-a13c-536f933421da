{"configurations": [{"name": "Linux", "includePath": ["${workspaceFolder}", "${workspaceFolder}/libpcap", "${workspaceFolder}/libpcap/pcap", "/usr/include", "/usr/local/include"], "defines": ["HAVE_CONFIG_H", "BUILDING_PCAP", "DEBUG", "_GNU_SOURCE"], "compilerPath": "/usr/bin/gcc", "cStandard": "c11", "cppStandard": "c++17", "intelliSenseMode": "linux-gcc-x64", "compileCommands": "${workspaceFolder}/compile_commands.json", "configurationProvider": "ms-vscode.cpptools"}], "version": 4}