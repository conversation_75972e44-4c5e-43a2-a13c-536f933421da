    1  1975-04-27 03:21:36.131862 TIPC v5.0 226.0.0 > 64.14.1536, headerlength 56 bytes, MessageSize 51914 bytes, Link Changeover Protocol internal, messageType Unknown (0xcacacaca) [|tipc]
    2  [Invalid header: len==0]
    3  1975-05-02 05:06:08.999999 IP (tos 0x0, ttl 14, id 44815, offset 0, flags [+, DF, rsvd], proto RSVP (46), length 40, bad cksum 3280 (->c411)!)
    ************* > **************: 
	RSVPv1 Hello Message (20), Flags: [none], length: 16384, ttl: 0, checksum: 0x000e
	  Class Type (old) Object (125) Flags: [reject if unknown], Class-Type: 1 (1), length: 4
	  ERROR: object is too short
